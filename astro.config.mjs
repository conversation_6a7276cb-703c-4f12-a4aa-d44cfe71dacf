import mdx from "@astrojs/mdx";
import sitemap from "@astrojs/sitemap";
import tailwind from "@astrojs/tailwind";
import pagefind from "astro-pagefind";
import { defineConfig } from "astro/config";
import path from "path";

// https://astro.build/config
export default defineConfig({
  site: "https://valonmulolli.github.io",
  base: "/v470n/",
  output: "static",
  build: {
    inlineStylesheets: "auto",
  },
  vite: {
    resolve: {
      alias: {
        "@components": path.resolve("./src/components"),
        "@layouts": path.resolve("./src/layouts"),
        "@lib": path.resolve("./src/lib"),
        "@consts": path.resolve("./src/consts.ts"),
        "@": path.resolve("./src"),
      },
    },
    build: {
      cssMinify: true,
      minify: true,
    },
  },
  integrations: [
    tailwind(),
    sitemap(),
    mdx(),
    pagefind({
      indexing: {
        verbose: true,
        excludeSelectors: ["nav", "footer", ".toc"],
      },
    }),
  ],
  markdown: {
    shikiConfig: {
      theme: "css-variables",
    },
  },
});
