<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<meta property="og:title" content="Valon Mulolli | CV" />
		<meta
			property="og:description"
			content="Full-stack developer specializing in Go, Rust, and modern web technologies. Experienced in building scalable microservices, data engineering solutions, and developer tools. Passionate about systems programming and creating efficient, maintainable code."
		/>
		<meta property="og:url" content="https://valon.dev/" />
		<meta property="og:type" content="profile" />
		<meta property="profile:first_name" content="Valon" />
		<meta property="profile:last_name" content="<PERSON><PERSON><PERSON>" />
		<meta property="profile:username" content="valon.dev" />
		<meta property="profile:gender" content="male" />

		<title>Valon Mulolli | CV</title>

		<link rel="stylesheet" href="css/bootstrap.min.css" />
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
		/>
		<link rel="stylesheet" href="css/github-markdown.min.css" />
		<link rel="stylesheet" href="css/styles.css" />
	</head>
	<body class="container my-5 markdown-body markdown-body-website">
		<div class="container" style="text-align: center">
			<p class="text-center fs-1 mb-2">Valon Mulolli</p>
			<div class="row justify-content-center">
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-brands fa-github"></i></a>

					<a href="https://github.com/valonmulolli">valonmulolli</a>
				</div>
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-solid fa-envelope"></i></i></a>
					<a href="mailto:<EMAIL>"><EMAIL></a>
				</div>
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-brands fa-linkedin-in"></i></a>
					<a href="https://www.linkedin.com/in/valonmulolli/">Valon Mulolli</a>
				</div>
				<div class="my-2 align-center">
					<i class="fa-solid fa-location-dot"></i>
					Pristine Kosovo
				</div>
				<h3 class="mb-2 align-center justify-content-center">
					Full-Stack Developer | Go • Rust • Data Engineering
				<h3>
			</div>
		</div>

		<h2 class="my-3">SUMMARY</h2>

		<p class="indent mb-2">

		<div>
			I started programming in 2012 during my computer science studies, where Java was part of the curriculum. After a seven-year break, I returned as a self-taught programmer in 2020, initially focusing on JavaScript and web development, but have since evolved into systems programming and backend development.
	</div>
	<div>
		Systems & Backend Developer: Currently specializing in Go and Rust for building high-performance, scalable applications. Experienced in microservices architecture, containerization with Docker and Kubernetes, and designing robust distributed systems. My focus has shifted from frontend frameworks to systems-level programming and infrastructure.
	</div>
	<div>
		Data Engineering & DevOps: Proficient in building data pipelines using Apache Kafka, monitoring with Prometheus and Grafana, and managing databases including PostgreSQL and Redis. Experienced with cloud platforms (AWS, GCP) and modern DevOps practices including CI/CD pipelines and infrastructure as code.
	</div>
	<div>
		Developer Tools & Productivity: Passionate about developer experience and tooling. Built command-line applications, automated workflows, and maintain a highly customized Neovim setup for efficient development. Strong advocate for Linux (Arch Linux) and terminal-based development environments.
	</div>
	<div>
		Currently focused on expanding expertise in Rust for systems programming, data engineering solutions, and building developer tools that enhance productivity and code quality. Always eager to tackle complex technical challenges and contribute to open-source projects.
	</div>

		</p>

		<h2 class="my-2 ">EXPERIENCE</h2>

		<div class="row d-flex justify-content-between align-items-center">
			<div class="col col-auto text-start">
				<a class="fs-6"
					>Central Election Comission of Republic of Kosovo (‘QNR’)</a
				>
				<h2>Data entry</h2>
			</div>
			<div class="col col-auto text-end">(2011 - 2015)</div>
		</div>

		<div class="mb-1">
			<div>
				Organized and managed files with precision for optimal organization. Conducted thorough data analysis, implemented stringent quality control measures, and promptly resolved errors. Actively reported identified issues, thereby contributing to the maintenance of data accuracy. Executed precise and efficient data entry tasks, emphasizing both speed and accuracy. Maintained confidentiality and security while handling sensitive information, adhering strictly to privacy protocols and organizational policies.
			</div>
		</div>

		<div class="row d-flex justify-content-between align-items-center">
			<div class="col col-auto text-start">
					<a class="fs-6">Remote</a>
					<h2>Upwork</h2>
			</div>
			<div class="col col-auto text-end">2021</div>
		</div>
		
		<div class="mb-1">
			<div>
				For a year as a web developer at Upwork, I worked on remote projects. During this time, I worked on a variety of web development projects, collaborating with clients to offer customized solutions. Coding, troubleshooting, and guaranteeing the proper implementation of web-based initiatives were among my responsibilities. This experience sharpened my abilities in remote communication, project management, and delivering high-quality web solutions to clients all around the world.
			</div>
		</div>

		<h2 class="my-3">SKILLS</h2>

		<div class="row mt-1 pt-1">
			<div class="col-auto col-skills fw-bold">Programming Languages</div>
			<div class="col">
				<span>Go, Rust, Python</span>
				<span>JavaScript/TypeScript</span>
				<span>HTML, CSS, Tailwind CSS</span>
				<span class="fw-bold">Scripting: </span>
				<span>Bash, Lua</span>
			</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Backend & Systems</div>
			<div class="col">Go (Gin, Echo), Rust (Tokio, Serde), Flask, Microservices Architecture</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Frontend & Web</div>
			<div class="col">Astro, TypeScript, Tailwind CSS, Modern Web Standards</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Databases & Storage</div>
			<div class="col">PostgreSQL, Redis, Database Design, Query Optimization</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">DevOps & Infrastructure</div>
			<div class="col">Docker, Kubernetes, Linux (Arch), Git, GitHub Actions, CI/CD Pipelines</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Data Engineering</div>
			<div class="col">Apache Kafka, Prometheus, Grafana, Data Pipelines, Monitoring</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Cloud & Tools</div>
			<div class="col">AWS, Google Cloud Platform, Neovim, Command Line Tools</div>
		</div>
			
		<h2 class="my-3 py-2">CERTIFICATIONS</h2>
		<p>
			<a
				class="fs-6"
				href="https://www.hackerrank.com/certificates/4fb46be70fae"
				>JavaScript (Intermediate) Certificate</a
			>
			<span>(HackerRank)</span>
		</p>

    <div>
			<h2 class="my-3">PROJECTS</h2>
		</div>	

		<div>
			<section>
				<h3>CipherVault</h3>
				<div class="mb-2">
					<ul>
						<li>Description: A secure, cross-platform password manager built in Rust with advanced encryption</li>
						<li>Features: AES-256 encryption, secure master password handling, cross-platform CLI interface</li>
						<li>Architecture: Built with Rust for memory safety and performance, using industry-standard cryptographic libraries</li>
						<li>Security: Implements zero-knowledge architecture with local-only storage and secure key derivation</li>
					</ul>
					<li>Technologies: <i class="fas fa-code"></i> Rust, Cryptography, CLI Development</li>
				</div>
				<p>Check out source code on Github: <a href="https://github.com/valonmulolli/ciphervault">CipherVault</a></p>
			</div>
		</section>
			
		<div>
			<section>
				<ul>
					<li>Pre-configured Payload Config: Kickstart your project with a pre-configured Payload setup, saving you time on initial configurations.</li>
					<li>Authentication: Secure user authentication to control access and protect user data.</li>
					<li>Access Control: Define and manage user access levels to ensure a tailored and secure experience.</li>
					<li>Shopping Cart: Enable users to easily add, remove, and manage items in their shopping cart.</li>
					<li>Checkout: Streamlined checkout process for a hassle-free shopping experience.</li>
					<li>Paywall: Implement a paywall to provide exclusive access to premium content or features.</li>
					<li>Layout Builder: Flexible layout builder for easy customization of the website's appearance and structure.</li>
					<li>SEO: Optimize your website for search engines to enhance visibility and reach a wider audience.</li>
					
					<li>Authentication and Authorization:</li>
					
					<li>Payload Admin Bar: A Payload plugin for an admin toolbar, facilitating quick access to admin functionality.</li>
				</ul>
				
				<div>Stripe Integration:
					<ul>
						<li>@stripe/react-stripe-js: React components for Stripe.js, facilitating the integration of Stripe elements into your React app.</li>
						<li>@stripe/stripe-js: Stripe.js library, providing a JavaScript API for interacting with Stripe.</li>
					</ul>
				</div>
			</section>

			</div>


			<div>
				<section>
					<h2>Task-CLI</h2>
					<p>A modern CLI task management application built in Go using the Bubble Tea framework for interactive terminal UIs</p>
					<ul>
						<li>Interactive TUI: Built with Bubble Tea for a modern, responsive terminal interface</li>
						<li>Task Management: Create, list, update, and delete tasks with intuitive keyboard navigation</li>
						<li>Organized Workflow: Tasks organized into To Do, In Progress, and Done sections</li>
						<li>Enhanced Features: Customizable task categories, detailed descriptions, and progress tracking</li>
						<li>Performance: Leverages Go's concurrency and performance for smooth user experience</li>
					</ul>
					<ul>
						<li>Technologies: <i class="fas fa-code"></i> Go, Bubble Tea, Terminal UI, CLI Development</li>
					</ul>
					<p>Check out source code on Github: <a href="https://github.com/valonmulolli/task-cli.git">Task-CLI</a></p>
				</section>
		</div>
			<div>
				<section>
					<h3>Personal Blog & Portfolio</h3>
					<div class="mb-2">
						<ul>
							<li>Modern Static Site: Built with Astro for optimal performance and SEO</li>
							<li>Content Focus: Technical tutorials on Go, Rust, data engineering, and Linux/Neovim setup</li>
							<li>Tech Stack: Astro, TypeScript, Tailwind CSS, MDX for content authoring</li>
							<li>Features: Dark/light theme, search functionality, responsive design, tech badge system</li>
							<li>Deployment: GitHub Pages with automated CI/CD pipeline</li>
						</ul>
						<li>Technologies: <i class="fas fa-code"></i> Astro, TypeScript, Tailwind CSS, MDX</li>
					</div>
					<p>Check out the blog: <a href="https://valonmulolli.github.io/v470n/">v470n.dev</a></p>
				</section>
			</div>

			<div>
				<section>
					<h3>Data Engineering & Microservices Projects</h3>
					<div class="mb-2">
						<ul>
							<li>Microservices Architecture: Go-based services with Docker and Kubernetes orchestration</li>
							<li>Data Pipelines: Apache Kafka for real-time data streaming and processing</li>
							<li>Monitoring Stack: Prometheus for metrics collection and Grafana for visualization</li>
							<li>Database Management: PostgreSQL for relational data, Redis for caching and sessions</li>
							<li>Cloud Deployment: AWS and GCP integration with infrastructure as code</li>
						</ul>
						<li>Technologies: <i class="fas fa-code"></i> Go, Docker, Kubernetes, Kafka, Prometheus, Grafana</li>
					</div>
				</section>
			</div>

			<div>
				<section>

					<h3>Legacy Projects: Full-Stack Web Development</h3>
					<p>Earlier projects demonstrating full-stack development skills with JavaScript/TypeScript ecosystem:</p>
					<div class="mb-2">
						<h4>Social Media Clones (Twitter, LinkedIn, Threads)</h4>
						<ul>
							<li>Full-stack applications using React Native/Expo for mobile interfaces</li>
							<li>Backend APIs built with Express.js and Prisma ORM for database management</li>
							<li>Authentication systems using JWT and OAuth integration</li>
							<li>Real-time features, file uploads, and responsive UI components</li>
							<li>Technologies: TypeScript, React Native, Express.js, Prisma, PostgreSQL</li>
						</ul>
						<p>GitHub: <a href="https://github.com/valonmulolli/twitter-app">Twitter Clone</a> |
						<a href="https://github.com/valonmulolli/LinkedIn-app">LinkedIn Clone</a> |
						<a href="https://github.com/valonmulolli/threads-app">Threads Clone</a></p>
					</div>
				</section>
			</div>

		<script src="./theme.ts"></script>
		<script src="./pdf.ts"></script>
	</body>
</html>