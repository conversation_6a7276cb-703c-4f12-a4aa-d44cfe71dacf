<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<meta property="og:title" content="<PERSON><PERSON> Mulolli | CV" />
		<meta
			property="og:description"
			content="Self-taught developer transitioning from web development to backend and systems programming. Currently learning Go, Rust, and data engineering through hands-on projects and practical application."
		/>
		<meta property="og:url" content="https://valon.dev/" />
		<meta property="og:type" content="profile" />
		<meta property="profile:first_name" content="Valon" />
		<meta property="profile:last_name" content="<PERSON><PERSON><PERSON>" />
		<meta property="profile:username" content="valon.dev" />
		<meta property="profile:gender" content="male" />

		<title>Valon Mulolli | CV</title>

		<link rel="stylesheet" href="css/bootstrap.min.css" />
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
		/>
		<link rel="stylesheet" href="css/github-markdown.min.css" />
		<link rel="stylesheet" href="css/styles.css" />
	</head>
	<body class="container my-5 markdown-body markdown-body-website">
		<div class="container" style="text-align: center">
			<p class="text-center fs-1 mb-2">Valon Mulolli</p>
			<div class="row justify-content-center">
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-brands fa-github"></i></a>

					<a href="https://github.com/valonmulolli">valonmulolli</a>
				</div>
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-solid fa-envelope"></i></i></a>
					<a href="mailto:<EMAIL>"><EMAIL></a>
				</div>
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-brands fa-linkedin-in"></i></a>
					<a href="https://www.linkedin.com/in/valonmulolli/">Valon Mulolli</a>
				</div>
				<div class="my-2 align-center">
					<i class="fa-solid fa-location-dot"></i>
					Pristine Kosovo
				</div>
				<h3 class="mb-2 align-center justify-content-center">
					Self-Taught Developer | Learning Go • Rust • Data Engineering
				<h3>
			</div>
		</div>

		<h2 class="my-3">SUMMARY</h2>

		<p class="indent mb-2">

		<div>
			I started programming in 2012 during my computer science studies with Java. After a seven-year break, I returned in 2020 as a self-taught programmer, initially mastering JavaScript and web development through online resources, documentation, and hands-on projects. Now self-directing my learning toward backend development and systems programming.
	</div>
	<div>
		Self-Taught Backend & Systems: Independently learning Go and Rust through official documentation, community resources, and building practical projects. Self-directed study of microservices architecture, Docker containerization, and distributed systems concepts. Demonstrating ability to learn complex technologies without formal instruction.
	</div>
	<div>
		Independent Data Engineering & DevOps Learning: Self-teaching data pipeline concepts with Apache Kafka, monitoring with Prometheus and Grafana, and database management with PostgreSQL and Redis. Hands-on learning with cloud platforms (AWS, GCP) and DevOps practices through personal experimentation and online courses.
	</div>
	<div>
		Self-Optimized Development Environment: Passionate about productivity and self-directed learning. Built command-line tools as learning exercises, automated personal workflows, and self-configured a highly customized Neovim setup. Self-taught Linux (Arch Linux) administration and terminal-based development workflows.
	</div>
	<div>
		Continuous Self-Learning: Currently deepening Rust knowledge through practice and community engagement, exploring data engineering through hands-on projects, and building a portfolio that demonstrates self-taught skills. Strong track record of independent learning and applying new technologies practically.
	</div>

		</p>

		<h2 class="my-2 ">EXPERIENCE</h2>

		<div class="row d-flex justify-content-between align-items-center">
			<div class="col col-auto text-start">
				<a class="fs-6"
					>Central Election Comission of Republic of Kosovo (‘QNR’)</a
				>
				<h2>Data entry</h2>
			</div>
			<div class="col col-auto text-end">(2011 - 2015)</div>
		</div>

		<div class="mb-1">
			<div>
				Organized and managed files with precision for optimal organization. Conducted thorough data analysis, implemented stringent quality control measures, and promptly resolved errors. Actively reported identified issues, thereby contributing to the maintenance of data accuracy. Executed precise and efficient data entry tasks, emphasizing both speed and accuracy. Maintained confidentiality and security while handling sensitive information, adhering strictly to privacy protocols and organizational policies.
			</div>
		</div>

		<div class="row d-flex justify-content-between align-items-center">
			<div class="col col-auto text-start">
					<a class="fs-6">Remote</a>
					<h2>Upwork</h2>
			</div>
			<div class="col col-auto text-end">2021</div>
		</div>
		
		<div class="mb-1">
			<div>
				For a year as a web developer at Upwork, I worked on remote projects. During this time, I worked on a variety of web development projects, collaborating with clients to offer customized solutions. Coding, troubleshooting, and guaranteeing the proper implementation of web-based initiatives were among my responsibilities. This experience sharpened my abilities in remote communication, project management, and delivering high-quality web solutions to clients all around the world.
			</div>
		</div>

		<h2 class="my-3">SKILLS</h2>

		<div class="row mt-1 pt-1">
			<div class="col-auto col-skills fw-bold">Programming Languages</div>
			<div class="col">
				<span>JavaScript/TypeScript (Self-taught, Proficient)</span>
				<span>Go, Rust, Python (Self-learning)</span>
				<span>HTML, CSS, Tailwind CSS</span>
				<span class="fw-bold">Scripting: </span>
				<span>Bash, Lua (Self-taught)</span>
			</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Backend & Systems (Self-Learning)</div>
			<div class="col">Go (Gin framework), Rust (personal projects), Flask, Microservices concepts</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Frontend & Web (Self-Taught)</div>
			<div class="col">Astro, TypeScript, Tailwind CSS, React, Next.js, Modern Web Standards</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Databases & Storage</div>
			<div class="col">PostgreSQL, Redis (self-learning), Database fundamentals</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">DevOps & Infrastructure (Self-Learning)</div>
			<div class="col">Docker, Kubernetes (basics), Linux (Arch - daily use), Git, GitHub Actions</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Data Engineering (Self-Exploring)</div>
			<div class="col">Apache Kafka, Prometheus, Grafana (independent learning through projects)</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Self-Configured Environment</div>
			<div class="col">Neovim (custom config), Linux (Arch), Command Line Tools, Git workflow</div>
		</div>
			
		<h2 class="my-3 py-2">CERTIFICATIONS</h2>
		<p>
			<a
				class="fs-6"
				href="https://www.hackerrank.com/certificates/4fb46be70fae"
				>JavaScript (Intermediate) Certificate</a
			>
			<span>(HackerRank)</span>
		</p>

    <div>
			<h2 class="my-3">PROJECTS</h2>
		</div>	

		<div>
			<section>
				<h3>CipherVault</h3>
				<div class="mb-2">
					<ul>
						<li>Description: Self-taught Rust project - password manager built to learn systems programming independently</li>
						<li>Self-Learning Goals: Teaching myself Rust ownership model, memory safety, and cryptographic concepts</li>
						<li>Features: CLI interface, file-based storage, implementing encryption through self-study</li>
						<li>Self-Developed Skills: Rust fundamentals, CLI development patterns, security principles</li>
					</ul>
					<li>Technologies: <i class="fas fa-code"></i> Rust (self-taught), CLI Development</li>
				</div>
				<p>Check out source code on Github: <a href="https://github.com/valonmulolli/ciphervault">CipherVault</a></p>
			</div>
		</section>
			



			<div>
				<section>
					<h2>Task-CLI</h2>
					<p>Self-taught Go project - CLI task management application built to independently learn Go and TUI development</p>
					<ul>
						<li>Self-Learning Goals: Teaching myself Go syntax, project structure, and terminal UI concepts</li>
						<li>Features: Task CRUD operations, terminal interface using Bubble Tea framework (learned independently)</li>
						<li>Self-Developed Skills: Go fundamentals, CLI development patterns, terminal UI libraries</li>
						<li>Project Structure: Organized workflow with To Do, In Progress, and Done sections</li>
						<li>Independent Learning: Working with Go modules, self-teaching concurrency concepts</li>
					</ul>
					<ul>
						<li>Technologies: <i class="fas fa-code"></i> Go (self-taught), Bubble Tea, CLI Development</li>
					</ul>
					<p>Check out source code on Github: <a href="https://github.com/valonmulolli/task-cli.git">Task-CLI</a></p>
				</section>
		</div>
			<div>
				<section>
					<h3>Personal Blog & Portfolio</h3>
					<div class="mb-2">
						<ul>
							<li>Modern Static Site: Built with Astro to learn modern web development practices</li>
							<li>Content Focus: Documenting my learning journey in Go, Rust, data engineering, and Linux/Neovim</li>
							<li>Tech Stack: Astro, TypeScript, Tailwind CSS, MDX for content authoring</li>
							<li>Features: Dark/light theme, search functionality, responsive design, custom tech badge components</li>
							<li>Deployment: GitHub Pages with automated CI/CD pipeline for learning DevOps concepts</li>
						</ul>
						<li>Technologies: <i class="fas fa-code"></i> Astro, TypeScript, Tailwind CSS, MDX</li>
					</div>
					<p>Check out the blog: <a href="https://valonmulolli.github.io/v470n/">v470n.dev</a></p>
				</section>
			</div>

			<div>
				<section>
					<h3>Learning Projects: Data Engineering & Microservices</h3>
					<div class="mb-2">
						<ul>
							<li>Microservices Exploration: Learning Go-based service architecture with Docker containerization</li>
							<li>Data Pipeline Tutorials: Following Apache Kafka tutorials for understanding data streaming concepts</li>
							<li>Monitoring Setup: Experimenting with Prometheus and Grafana for metrics and visualization</li>
							<li>Database Practice: Working with PostgreSQL and Redis through personal projects and tutorials</li>
							<li>Cloud Learning: Exploring AWS and GCP services through free tier and educational resources</li>
						</ul>
						<li>Technologies: <i class="fas fa-code"></i> Go (learning), Docker, Kafka (tutorials), Prometheus, Grafana</li>
					</div>
				</section>
			</div>

			<div>
				<section>

					<h3>Previous Projects: Full-Stack Web Development</h3>
					<p>Earlier projects that built my foundation in JavaScript/TypeScript and full-stack development:</p>
					<div class="mb-2">
						<h4>Social Media Clones (Twitter, LinkedIn, Threads)</h4>
						<ul>
							<li>Full-stack learning projects using React Native/Expo for mobile development experience</li>
							<li>Backend APIs built with Express.js and Prisma ORM to understand database integration</li>
							<li>Implemented authentication systems using JWT to learn security concepts</li>
							<li>Features: Real-time updates, file uploads, responsive UI - building practical development skills</li>
							<li>Technologies: TypeScript, React Native, Express.js, Prisma, PostgreSQL</li>
						</ul>
						<p>GitHub: <a href="https://github.com/valonmulolli/twitter-app">Twitter Clone</a> |
						<a href="https://github.com/valonmulolli/LinkedIn-app">LinkedIn Clone</a> |
						<a href="https://github.com/valonmulolli/threads-app">Threads Clone</a></p>
					</div>
				</section>
			</div>

		<script src="./theme.ts"></script>
		<script src="./pdf.ts"></script>
	</body>
</html>