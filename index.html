<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<meta property="og:title" content="Valon Mulolli | CV" />
		<meta
			property="og:description"
			content="Passionate React and React Native developer showcasing a diverse portfolio of web and mobile applications. Experienced in creating responsive and user-friendly interfaces. Explore my projects and discover how I bring ideas to life through code."
		/>
		<meta property="og:url" content="https://valon.dev/" />
		<meta property="og:type" content="profile" />
		<meta property="profile:first_name" content="Valon" />
		<meta property="profile:last_name" content="<PERSON><PERSON><PERSON>" />
		<meta property="profile:username" content="valon.dev" />
		<meta property="profile:gender" content="male" />

		<title>Valon Mulolli | CV</title>

		<link rel="stylesheet" href="css/bootstrap.min.css" />
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
		/>
		<link rel="stylesheet" href="css/github-markdown.min.css" />
		<link rel="stylesheet" href="css/styles.css" />
	</head>
	<body class="container my-5 markdown-body markdown-body-website">
		<div class="container" style="text-align: center">
			<p class="text-center fs-1 mb-2">Valon Mulolli</p>
			<div class="row justify-content-center">
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-brands fa-github"></i></a>

					<a href="https://github.com/valonmulolli">valonmulolli</a>
				</div>
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-solid fa-envelope"></i></i></a>
					<a href="mailto:<EMAIL>"><EMAIL></a>
				</div>
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-brands fa-linkedin-in"></i></a>
					<a href="https://www.linkedin.com/in/valonmulolli/">Valon Mulolli</a>
				</div>
				<div class="my-2 align-center">
					<i class="fa-solid fa-location-dot"></i>
					Pristine Kosovo
				</div>
				<h3 class="mb-2 align-center justify-content-center">
					React/React-Native
				<h3>
			</div>
		</div>

		<h2 class="my-3">SUMMARY</h2>

		<p class="indent mb-2">

		<div>
			I started programming in 2012 during my computer science studies, where Java was part of the curriculum. After a seven-year break, I returned as a self-taught programmer in 2020, focusing on JavaScript for web, mobile, and backend development.
	</div>
	<div>
		Frontend & Mobile Developer: I have experience with React Native, TypeScript, and Node.js, and I have been actively expanding my skills in these technologies. My journey has involved transitioning from vanilla JavaScript to frameworks like Next.js, and venturing into mobile development with React Native and Expo, which marked a significant milestone in my career
	</div>
	<div>
		Backend Developer: Explored Node.js, Golang, and Java with Spring Boot, gaining insights into scalable backend practices and designing robust server architectures. Mastered data handling and optimized performance, ensuring a solid foundation for future projects.
	</div>
	<div>
		I utilized Docker to containerize my Spring Boot application, enabling it to run in an isolated environment. This approach streamlined the deployment process and ensured consistent behavior across various environments. By leveraging Docker, I optimized the application's development and deployment, laying a solid foundation for future projects.
	</div>
	<div>
		Reflecting on this journey, I'm committed to continuous learning, fueled by the challenges of programming. Eager for more ambitious projects, I look forward to the unfolding chapters of my coding journey.
	</div>

		</p>

		<h2 class="my-2 ">EXPERIENCE</h2>

		<div class="row d-flex justify-content-between align-items-center">
			<div class="col col-auto text-start">
				<a class="fs-6"
					>Central Election Comission of Republic of Kosovo (‘QNR’)</a
				>
				<h2>Data entry</h2>
			</div>
			<div class="col col-auto text-end">(2011 - 2015)</div>
		</div>

		<div class="mb-1">
			<div>
				Organized and managed files with precision for optimal organization. Conducted thorough data analysis, implemented stringent quality control measures, and promptly resolved errors. Actively reported identified issues, thereby contributing to the maintenance of data accuracy. Executed precise and efficient data entry tasks, emphasizing both speed and accuracy. Maintained confidentiality and security while handling sensitive information, adhering strictly to privacy protocols and organizational policies.
			</div>
		</div>

		<div class="row d-flex justify-content-between align-items-center">
			<div class="col col-auto text-start">
					<a class="fs-6">Remote</a>
					<h2>Upwork</h2>
			</div>
			<div class="col col-auto text-end">2021</div>
		</div>
		
		<div class="mb-1">
			<div>
				For a year as a web developer at Upwork, I worked on remote projects. During this time, I worked on a variety of web development projects, collaborating with clients to offer customized solutions. Coding, troubleshooting, and guaranteeing the proper implementation of web-based initiatives were among my responsibilities. This experience sharpened my abilities in remote communication, project management, and delivering high-quality web solutions to clients all around the world.
			</div>
		</div>

		<h2 class="my-3">SKILLS</h2>

		<div class="row mt-1 pt-1">
			<div class="col-auto col-skills fw-bold">Programming Languages</div>
			<div class="col">
				<span>JavaScript/TypeScript</span>
				<span>Html,Css,Sass,Tailwind</span>
				<span class="fw-bold">Scripting Languages: </span>
				<span>bash,powershell,Lua</span>

			</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Frameworks</div>
			<div class="col">Next JS,Express.js,React Native, Expo</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Libraries</div>
			<div class="col">React Framer Motion Three js React Three Fiber</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Database/API Integration</div>
			<div class="col">PostgreSQL, AWS, MongoDB, Prisma</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">Other</div>
			<div class="col">Version Control Systems Git GitHub Actions Documentation, Debugging & Troubleshooting,Docker, CI/CD,package managers:npm,yarn,pnpm and build tools Make,Maven,Webpack
			</div>
		</div>

		<div class="row">
			<div class="col-auto col-skills fw-bold">IDE/Text Editors</div>
			<div class="col">VSCode, Android Studio, IntelliJ IDEA VIM NeoVim</div>
		</div>
			
		<h2 class="my-3 py-2">CERTIFICATIONS</h2>
		<p>
			<a
				class="fs-6"
				href="https://www.hackerrank.com/certificates/4fb46be70fae"
				>JavaScript (Intermediate) Certificate</a
			>
			<span>(HackerRank)</span>
		</p>

    <div>
			<h2 class="my-3">PROJECTS</h2>
		</div>	

		<div>
			<section>
				<h3>ECOM</h3>
				<div class="mb-2">
					<ul>
						<li>Description: ECOM e-commerce CMS web-application using Next.js 14</li>
						<li>Pre-configured Payload Config: Kickstart your project with a pre-configured Payload setup, saving you time on initial configurations.</li>
					</ul>
					<li>Technologies: <i class="fas fa-code"></i> TypeScript</li>
				</div>
				<p>Check out source code on Github: <a href="https://github.com/valonmulolli/ecom">Ecom</a></p>
			</div>		
		</section>
			
		<div>
			<section>
				<ul>
					<li>Pre-configured Payload Config: Kickstart your project with a pre-configured Payload setup, saving you time on initial configurations.</li>
					<li>Authentication: Secure user authentication to control access and protect user data.</li>
					<li>Access Control: Define and manage user access levels to ensure a tailored and secure experience.</li>
					<li>Shopping Cart: Enable users to easily add, remove, and manage items in their shopping cart.</li>
					<li>Checkout: Streamlined checkout process for a hassle-free shopping experience.</li>
					<li>Paywall: Implement a paywall to provide exclusive access to premium content or features.</li>
					<li>Layout Builder: Flexible layout builder for easy customization of the website's appearance and structure.</li>
					<li>SEO: Optimize your website for search engines to enhance visibility and reach a wider audience.</li>
					
					<li>Authentication and Authorization:</li>
					
					<li>Payload Admin Bar: A Payload plugin for an admin toolbar, facilitating quick access to admin functionality.</li>
				</ul>
				
				<div>Stripe Integration:
					<ul>
						<li>@stripe/react-stripe-js: React components for Stripe.js, facilitating the integration of Stripe elements into your React app.</li>
						<li>@stripe/stripe-js: Stripe.js library, providing a JavaScript API for interacting with Stripe.</li>
					</ul>
				</div>
			</section>

			</div>

			<div>
				<h3>LinkedIn Clone</h3>
				
				<section>
					<h4>User Authentication:</h4>
					<ul>
						<li>Implemented Clerk for OAuth login flows.</li>
						<li>Utilized StepZen's GraphQL API for backend.</li>
					</ul>
				</section>
				
				<section>
					<h4>Data Retrieval and Pagination:</h4>
					<ul>
						<li>Consumed LinkedIn GraphQL API for profile, connections, and posts.</li>
						<li>Paginated queries for on-demand data loading.</li>
						<li>Authenticated API calls with Clerk tokens.</li>
					</ul>
				</section>
				
				<section>
					<h4>Technology Integration:</h4>
					<ul>
						<li>Integrated Expo, Clerk, and StepZen for responsive UI and seamless backend interactions.</li>
					</ul>
				</section>
				
				<p>Technologies: <span class="technology-icon"></span> TypeScript</p>
				<p>Check out source code on Github: <a href="https://github.com/valonmulolli/LinkedIn-app">LinkedIn app-clone</a></p>
			</div>		

			<div>
				<h2>Threads Clone </h2>
				<p>Threads clone using Expo and React Native. Utilized Expo router, Faker.js for dummy data, and various UI components.</p>
				<ul>
					<li>Screens: Implemented feed, thread details, create thread, and user profile using Expo components.</li>
					<li>Navigation: Expo router manages navigation with tab and stack navigators.</li>
					<li>Thread Display: Utilized RecyclerListView for dynamic thread display.</li>
					<li>Create Thread: Users can upload images and content with realistic previews using Faker.js.</li>
					<li>User Profile: Profile screen with details generated by Faker for a realistic prototype UI.</li>
				</ul>
				<ul>
					<li>Technologies: <i class="fas fa-code"></i> TypeScript</li>
				</ul>
				<p>Check out source code on Github: <a href="https://github.com/valonmulolli/threads-app">Threads app-clone</a></p>
			</div>
			<div>
				<section>
					<h2>Task-cli</h2>
					<p>A simple CLI tasks app built in Go using the Bubble Tea</p>
					<ul>
						<li>Create, list, update, and delete tasks</li>
						<li>Interactive user interface with progress bars and text inputs.</li>
						<li>Organize tasks into different sections:</li>
						<ul>
							<li>To Do Section</li>
							<li>InProgress Section</li>
							<li>Done Section</li>
						</ul>
						<li>Additional features:</li>
						<ul>
							<li>Customizable task categories</li>
							<li>Add description to each task</li>
						</ul>
					</ul>
					<ul>
						<li>Technologies: <i class="fas fa-code"></i> Go</li>
					</ul>
					<p>Check out source code on Github: <a href="https://github.com/valonmulolli/task-cli.git">Task-cli</a></p>
				</section>
		</div>
			<div>
				<section>

					<h3>Building a Twitter Clone: A Tech Stack Showcase</h3>
					<div>
						<div>
							<h3>Twitter Clone</h3>
							<div>
								<li>Twitter clone using Expo and Prisma for frontend and backend, respectively.</li>
								<li>Utilized Expo SDK for UI components and implemented user authentication with JSON Web tokens.</li>
								<li>Technologies: <i class="fas fa-code"></i> TypeScript</li>
							</div>
							<p>Check out source code on Github: <a href="https://github.com/valonmulolli/twitter-app">Twitter app-clone</a></p>
						</div>
					</div>
					<div>
						<h3>Backend</h3>
						<div>
							<ul>
								<li>@aws-sdk/client-ses: Utilized for interacting with Amazon Simple Email Service (SES) to handle email-related functionalities.</li>
								<li>@prisma/client: Prisma Client for database access and management, ensuring efficient and type-safe queries.</li>
								<li>Express js: A fast and minimal web framework for Node.js, used for building robust and scalable APIs.</li>
								<li>jsonwebtoken: Implements JSON Web Token (JWT) authentication for secure user sessions and access control.</li>
							</ul>
						</div>
					</div>
				</section>
			</div>

		<script src="./theme.ts"></script>
		<script src="./pdf.ts"></script>
	</body>
</html>