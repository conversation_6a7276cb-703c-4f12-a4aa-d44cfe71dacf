export type CollectionName = "blog" | "projects" | "tutorials";

export type GlobalSite = {
  title: string;
  description: string;
  author: string;
  authorPhotoSrc: string;
  logo?:
    | {
        darkThemeSrc: string;
        lightThemeSrc: string;
      }
    | undefined;
};

export const GLOBAL: GlobalSite = {
  title: "valon",
  description: "a minimalistic blog+portfolio Astro theme",
  author: "<PERSON><PERSON>",
  authorPhotoSrc: "/vm.jpeg",
};

type CollectionSite = {
  pageSize: number;
};

type HomeSite = {
  blogEntries?: number;
  projectEntries?: number;
  tutorialEntries?: number;
};

export const HOME: HomeSite = {
  blogEntries: 5,
  projectEntries: 3,
  tutorialEntries: 3,
};

type BlogSite = CollectionSite & {
  license: {
    name: string;
    href: string;
  };
};

export const BLOG: BlogSite = {
  pageSize: 10,
  license: {
    name: "CC BY-NC-ND 4.0",
    href: "https://creativecommons.org/licenses/by-nc-nd/4.0",
  },
};

export const PROJECTS: CollectionSite = {
  pageSize: 10,
};

export const TUTORIALS: CollectionSite = {
  pageSize: 10,
};

export const TAGS: CollectionSite = {
  pageSize: 10,
};

type ContactInfo = {
  type: string;
  href: string;
  displayAs?: string;
};

type ContactSite = ContactInfo[];

export const CONTACT: ContactSite = [
  {
    type: "Email",
    href: "mailto:<EMAIL>",
    displayAs: "<EMAIL>",
  },
  {
    type: "X",
    href: "https://x.com/valonmulolli",
    displayAs: "@valonmulolli",
  },
  {
    type: "GitHub",
    href: "https://github.com/valonmulolli",
  },
  {
    type: "LinkedIn",
    href: "https://www.linkedin.com/in/valonmulolli/",
  },
];
