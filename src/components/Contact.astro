---
import { CONTACT } from "@consts";

type Props = {
  small?: boolean | undefined;
};

const { small } = Astro.props;
---

<p class="mb-0" style={small ? "font-size: 0.875rem" : ""}>
  {
    CONTACT.map((c, index) => (
      <>
        <a href={c.href} target="_blank">
          {c.displayAs ?? c.type}
        </a>
        <span class="mx-2">{index < CONTACT.length - 1 && " | "}</span>
      </>
    ))
  }
</p>
