---
interface Props {
  tech: string;
  variant?: "default" | "flat" | "for-the-badge";
  color?: string;
  link?: boolean;
}

const { tech, variant = "default", color, link = false } = Astro.props;

// Tech stack badges based on md-badges
const badges: Record<string, { url: string; link: string }> = {
  // Frontend/Web
  astro: {
    url: "https://img.shields.io/badge/Astro-FF5D01?style={style}&logo=astro&logoColor=white",
    link: "https://astro.build/",
  },
  typescript: {
    url: "https://img.shields.io/badge/TypeScript-3178C6?style={style}&logo=typescript&logoColor=white",
    link: "https://www.typescriptlang.org/",
  },
  tailwind: {
    url: "https://img.shields.io/badge/Tailwind_CSS-06B6D4?style={style}&logo=tailwindcss&logoColor=white",
    link: "https://tailwindcss.com/",
  },
  javascript: {
    url: "https://img.shields.io/badge/JavaScript-F7DF1E?style={style}&logo=javascript&logoColor=black",
    link: "https://developer.mozilla.org/en-US/docs/Web/JavaScript",
  },
  lua: {
    url: "https://img.shields.io/badge/Lua-2C2D72?style={style}&logo=lua&logoColor=white",
    link: "https://www.lua.org/",
  },
  neovim: {
    url: "https://img.shields.io/badge/Neovim-57A143?style={style}&logo=neovim&logoColor=white",
    link: "https://neovim.io/",
  },

  // Backend/Languages
  go: {
    url: "https://img.shields.io/badge/Go-00ADD8?style={style}&logo=go&logoColor=white",
    link: "https://golang.org/",
  },
  rust: {
    url: "https://img.shields.io/badge/Rust-000000?style={style}&logo=rust&logoColor=white",
    link: "https://www.rust-lang.org/",
  },
  python: {
    url: "https://img.shields.io/badge/Python-3776AB?style={style}&logo=python&logoColor=white",
    link: "https://www.python.org/",
  },
  flask: {
    url: "https://img.shields.io/badge/Flask-000000?style={style}&logo=flask&logoColor=white",
    link: "https://flask.palletsprojects.com/",
  },

  // Databases
  postgresql: {
    url: "https://img.shields.io/badge/PostgreSQL-4169E1?style={style}&logo=postgresql&logoColor=white",
    link: "https://www.postgresql.org/",
  },
  redis: {
    url: "https://img.shields.io/badge/Redis-DC382D?style={style}&logo=redis&logoColor=white",
    link: "https://redis.io/",
  },

  // DevOps/Infrastructure
  docker: {
    url: "https://img.shields.io/badge/Docker-2496ED?style={style}&logo=docker&logoColor=white",
    link: "https://www.docker.com/",
  },
  kubernetes: {
    url: "https://img.shields.io/badge/Kubernetes-326CE5?style={style}&logo=kubernetes&logoColor=white",
    link: "https://kubernetes.io/",
  },
  linux: {
    url: "https://img.shields.io/badge/Linux-FCC624?style={style}&logo=linux&logoColor=black",
    link: "https://www.linux.org/",
  },
  arch: {
    url: "https://img.shields.io/badge/Arch_Linux-1793D1?style={style}&logo=archlinux&logoColor=white",
    link: "https://archlinux.org/",
  },

  // Tools
  git: {
    url: "https://img.shields.io/badge/Git-F05032?style={style}&logo=git&logoColor=white",
    link: "https://git-scm.com/",
  },
  github: {
    url: "https://img.shields.io/badge/GitHub-181717?style={style}&logo=github&logoColor=white",
    link: "https://github.com/",
  },
  vscode: {
    url: "https://img.shields.io/badge/VS_Code-007ACC?style={style}&logo=visualstudiocode&logoColor=white",
    link: "https://code.visualstudio.com/",
  },

  // Data Engineering
  "apache-kafka": {
    url: "https://img.shields.io/badge/Apache_Kafka-231F20?style={style}&logo=apachekafka&logoColor=white",
    link: "https://kafka.apache.org/",
  },
  prometheus: {
    url: "https://img.shields.io/badge/Prometheus-E6522C?style={style}&logo=prometheus&logoColor=white",
    link: "https://prometheus.io/",
  },
  grafana: {
    url: "https://img.shields.io/badge/Grafana-F46800?style={style}&logo=grafana&logoColor=white",
    link: "https://grafana.com/",
  },

  // Cloud Platforms
  aws: {
    url: "https://img.shields.io/badge/AWS-232F3E?style={style}&logo=amazonaws&logoColor=white",
    link: "https://aws.amazon.com/",
  },
  gcp: {
    url: "https://img.shields.io/badge/Google_Cloud-4285F4?style={style}&logo=googlecloud&logoColor=white",
    link: "https://cloud.google.com/",
  },
};

const badge = badges[tech.toLowerCase()];
if (!badge) {
  console.warn(`Badge not found for tech: ${tech}`);
}

const style =
  variant === "flat"
    ? "flat"
    : variant === "for-the-badge"
      ? "for-the-badge"
      : "flat-square";
const badgeUrl = badge?.url.replace("{style}", style) || "";
const customColor = color ? `&color=${color}` : "";
const finalUrl = badgeUrl + customColor;
---

{
  badge &&
    (link ? (
      <a
        href={badge.link}
        target="_blank"
        rel="noopener noreferrer"
        class="inline-block"
      >
        <img src={finalUrl} alt={`${tech} badge`} class="inline-block" />
      </a>
    ) : (
      <img src={finalUrl} alt={`${tech} badge`} class="inline-block" />
    ))
}
