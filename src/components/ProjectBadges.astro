---
import TechBadge from "./TechBadge.astro";

interface Props {
  project:
    | "ciphervault"
    | "microservices"
    | "blog"
    | "dataengineering"
    | "custom";
  technologies?: string[];
  variant?: "default" | "flat" | "for-the-badge";
  link?: boolean;
}

const {
  project,
  technologies = [],
  variant = "flat",
  link = true,
} = Astro.props;

// Project-specific tech stacks
const projectStacks = {
  ciphervault: ["rust", "linux", "git", "github"],
  microservices: [
    "go",
    "docker",
    "kubernetes",
    "postgresql",
    "redis",
    "prometheus",
    "grafana",
  ],
  blog: ["astro", "typescript", "tailwind", "github"],
  dataengineering: [
    "python",
    "postgresql",
    "apache-kafka",
    "docker",
    "kubernetes",
    "prometheus",
  ],
  custom: technologies,
};

const techs = projectStacks[project] || technologies;
---

<div class="project-badges">
  <div class="flex flex-wrap gap-1.5 items-center">
    {
      techs.map((tech) => (
        <TechBadge tech={tech} variant={variant} link={link} />
      ))
    }
  </div>
</div>

<style>
  .project-badges {
    margin: 0.5rem 0;
  }
</style>
