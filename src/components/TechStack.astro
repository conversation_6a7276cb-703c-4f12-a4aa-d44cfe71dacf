---
import TechBadge from "./TechBadge.astro";

interface Props {
  technologies: string[];
  variant?: "default" | "flat" | "for-the-badge";
  link?: boolean;
  title?: string;
  className?: string;
}

const {
  technologies,
  variant = "default",
  link = true,
  title,
  className = "",
} = Astro.props;

// Predefined tech stacks for common use cases
const techStacks = {
  frontend: ["astro", "typescript", "tailwind", "javascript"],
  backend: ["go", "rust", "python", "flask"],
  database: ["postgresql", "redis"],
  devops: ["docker", "kubernetes", "linux", "arch"],
  tools: ["git", "github", "vscode"],
  dataengineering: [
    "python",
    "apache-kafka",
    "postgresql",
    "prometheus",
    "grafana",
  ],
  cloud: ["aws", "gcp", "docker", "kubernetes"],
  microservices: [
    "go",
    "docker",
    "kubernetes",
    "postgresql",
    "redis",
    "prometheus",
  ],
  webdev: ["astro", "typescript", "tailwind", "javascript", "git", "github"],
};

// If technologies is a string (predefined stack name), use that stack
const techs =
  typeof technologies === "string" && techStacks[technologies]
    ? techStacks[technologies]
    : Array.isArray(technologies)
      ? technologies
      : [];
---

<div class={`tech-stack ${className}`}>
  {
    title && (
      <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">
        {title}
      </h3>
    )
  }
  <div class="flex flex-wrap gap-2 items-center">
    {
      techs.map((tech) => (
        <TechBadge tech={tech} variant={variant} link={link} />
      ))
    }
  </div>
</div>

<style>
  .tech-stack {
    margin: 1rem 0;
  }

  .tech-stack img {
    transition: transform 0.2s ease-in-out;
  }

  .tech-stack a:hover img {
    transform: scale(1.05);
  }
</style>
