---
import Hash from "@components/Hash.astro";
import { resolvePath } from "@lib/utils";

interface Props {
  tag: string;
  entriesCount?: number | undefined;
}

const { tag, entriesCount } = Astro.props;
---

<a
  href={resolvePath(`/tags/${tag}`)}
  class="inline-flex items-center rounded border border-black/15 px-2 py-1 transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
>
  <Hash class="mr-1 h-4 w-4 text-gray-500" />
  {tag}
  {
    entriesCount !== undefined ? (
      <span class="ml-2 text-sm text-gray-500">({entriesCount})</span>
    ) : undefined
  }
</a>
