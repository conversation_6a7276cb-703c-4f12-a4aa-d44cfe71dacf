---
interface Props {
  class?: string | undefined;
}

const { class: className } = Astro.props;
---

<svg
  class={className}
  viewBox="0 0 24 24"
  width="1em"
  height="1em"
  fill="none"
  stroke="currentColor"
  stroke-width="2.5"
  stroke-linecap="round"
>
  <line x1="8" y1="3" x2="8" y2="21"></line>
  <line x1="16" y1="3" x2="16" y2="21"></line>
  <line x1="3" y1="9" x2="21" y2="9"></line>
  <line x1="3" y1="15" x2="21" y2="15"></line>
</svg>
