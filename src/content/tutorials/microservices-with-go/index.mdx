---
title: "Building Enterprise-Level Microservices with Go: Complete Guide"
description: "Master building production-ready, scalable microservices with Go - from basic setup to advanced enterprise patterns"
date: "2024-07-02"
lastUpdateDate: "2024-07-02"
difficulty: "advanced"
duration: "90 minutes"
ogImage: "/tutorials/go-language-features/og-image.png"
tags:
  - go
  - golang
  - microservices
  - enterprise
  - backend
  - api
  - distributed-systems
  - production
---

import Callout from "@components/Callout.astro";
import MdxPublicImage from "@components/MdxPublicImage.astro";
import TechStack from "@components/TechStack.astro";

## Introduction

This comprehensive guide will teach you how to build enterprise-level microservices with Go, covering everything from basic setup to advanced production patterns. We'll build a complete user management system that demonstrates real-world enterprise requirements including security, observability, resilience, and scalability.

<TechStack
  technologies={[
    "go",
    "docker",
    "kubernetes",
    "postgresql",
    "redis",
    "prometheus",
    "grafana",
  ]}
  title="Technologies Used in This Tutorial"
  variant="flat"
/>

<Callout type="info">
  This tutorial covers enterprise patterns used by companies like Google, Uber,
  Netflix, and Spotify. We'll implement industry-standard practices for
  authentication, monitoring, circuit breakers, distributed tracing, and more.
</Callout>

## What You'll Build

By the end of this tutorial, you'll have built a complete enterprise microservices system including:

- **User Service**: Core business logic with CRUD operations
- **Authentication Service**: JWT-based authentication with refresh tokens
- **API Gateway**: Request routing, rate limiting, and authentication
- **Event-Driven Architecture**: Async communication with message queues
- **Observability Stack**: Logging, metrics, and distributed tracing
- **Resilience Patterns**: Circuit breakers, retries, and timeouts

## Prerequisites

Before starting, ensure you have:

- **Go 1.21+** installed
- **Docker & Docker Compose** for containerization
- **PostgreSQL** knowledge for data persistence
- **Basic understanding** of microservices architecture
- **Familiarity** with REST APIs and HTTP protocols

## Architecture Overview

Our enterprise microservices architecture will include:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │────│  Auth Service   │────│  User Service   │
│  (Rate Limit,   │    │ (JWT, OAuth2)   │    │ (Business Logic)│
│   Load Balance) │    └─────────────────┘    └─────────────────┘
└─────────────────┘              │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Message Bus   │    │   Observability │    │    Database     │
│ (Redis/RabbitMQ)│    │ (Prometheus,    │    │  (PostgreSQL)   │
│                 │    │  Jaeger, ELK)   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 1. Enterprise Project Structure

First, let's establish a proper enterprise-grade project structure that supports multiple services, shared libraries, and clear separation of concerns.

### Directory Structure

```
enterprise-microservices/
├── cmd/                          # Application entry points
│   ├── user-service/
│   │   └── main.go
│   ├── auth-service/
│   │   └── main.go
│   └── api-gateway/
│       └── main.go
├── internal/                     # Private application code
│   ├── user/                     # User service domain
│   │   ├── handler/              # HTTP handlers
│   │   ├── service/              # Business logic
│   │   ├── repository/           # Data access
│   │   └── model/                # Domain models
│   ├── auth/                     # Auth service domain
│   └── gateway/                  # API Gateway
├── pkg/                          # Public libraries
│   ├── config/                   # Configuration management
│   ├── database/                 # Database utilities
│   ├── middleware/               # Shared middleware
│   ├── logger/                   # Logging utilities
│   ├── metrics/                  # Metrics collection
│   ├── tracing/                  # Distributed tracing
│   └── errors/                   # Error handling
├── api/                          # API definitions
│   ├── proto/                    # Protocol buffer definitions
│   └── openapi/                  # OpenAPI specifications
├── deployments/                  # Deployment configurations
│   ├── docker/
│   ├── kubernetes/
│   └── docker-compose.yml
├── scripts/                      # Build and deployment scripts
├── docs/                         # Documentation
├── go.mod
└── go.sum
```

### Initialize the Project

```bash
# Create the project structure
mkdir -p enterprise-microservices/{cmd/{user-service,auth-service,api-gateway},internal/{user/{handler,service,repository,model},auth,gateway},pkg/{config,database,middleware,logger,metrics,tracing,errors},api/{proto,openapi},deployments/{docker,kubernetes},scripts,docs}

cd enterprise-microservices

# Initialize Go module
go mod init github.com/yourusername/enterprise-microservices

# Add essential dependencies
go get github.com/gin-gonic/gin
go get github.com/golang-jwt/jwt/v5
go get gorm.io/gorm
go get gorm.io/driver/postgres
go get github.com/redis/go-redis/v9
go get github.com/prometheus/client_golang
go get go.opentelemetry.io/otel
go get github.com/sirupsen/logrus
go get github.com/spf13/viper
go get github.com/stretchr/testify
```

### Shared Configuration Package

```go
// pkg/config/config.go
package config

import (
    "fmt"
    "strings"
    "time"

    "github.com/spf13/viper"
)

type Config struct {
    Server   ServerConfig   `mapstructure:"server"`
    Database DatabaseConfig `mapstructure:"database"`
    Redis    RedisConfig    `mapstructure:"redis"`
    JWT      JWTConfig      `mapstructure:"jwt"`
    Metrics  MetricsConfig  `mapstructure:"metrics"`
    Tracing  TracingConfig  `mapstructure:"tracing"`
}

type ServerConfig struct {
    Port            string        `mapstructure:"port"`
    Host            string        `mapstructure:"host"`
    ReadTimeout     time.Duration `mapstructure:"read_timeout"`
    WriteTimeout    time.Duration `mapstructure:"write_timeout"`
    ShutdownTimeout time.Duration `mapstructure:"shutdown_timeout"`
}

type DatabaseConfig struct {
    Host         string `mapstructure:"host"`
    Port         int    `mapstructure:"port"`
    User         string `mapstructure:"user"`
    Password     string `mapstructure:"password"`
    DBName       string `mapstructure:"dbname"`
    SSLMode      string `mapstructure:"sslmode"`
    MaxOpenConns int    `mapstructure:"max_open_conns"`
    MaxIdleConns int    `mapstructure:"max_idle_conns"`
}

type RedisConfig struct {
    Host     string `mapstructure:"host"`
    Port     int    `mapstructure:"port"`
    Password string `mapstructure:"password"`
    DB       int    `mapstructure:"db"`
}

type JWTConfig struct {
    Secret           string        `mapstructure:"secret"`
    AccessTokenTTL   time.Duration `mapstructure:"access_token_ttl"`
    RefreshTokenTTL  time.Duration `mapstructure:"refresh_token_ttl"`
}

type MetricsConfig struct {
    Enabled bool   `mapstructure:"enabled"`
    Port    string `mapstructure:"port"`
}

type TracingConfig struct {
    Enabled     bool   `mapstructure:"enabled"`
    ServiceName string `mapstructure:"service_name"`
    Endpoint    string `mapstructure:"endpoint"`
}

func Load(configPath string) (*Config, error) {
    viper.SetConfigFile(configPath)
    viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
    viper.AutomaticEnv()

    // Set defaults
    setDefaults()

    if err := viper.ReadInConfig(); err != nil {
        return nil, fmt.Errorf("failed to read config file: %w", err)
    }

    var config Config
    if err := viper.Unmarshal(&config); err != nil {
        return nil, fmt.Errorf("failed to unmarshal config: %w", err)
    }

    return &config, nil
}

func setDefaults() {
    viper.SetDefault("server.port", "8080")
    viper.SetDefault("server.host", "0.0.0.0")
    viper.SetDefault("server.read_timeout", "15s")
    viper.SetDefault("server.write_timeout", "15s")
    viper.SetDefault("server.shutdown_timeout", "30s")

    viper.SetDefault("database.host", "localhost")
    viper.SetDefault("database.port", 5432)
    viper.SetDefault("database.sslmode", "disable")
    viper.SetDefault("database.max_open_conns", 25)
    viper.SetDefault("database.max_idle_conns", 5)

    viper.SetDefault("redis.host", "localhost")
    viper.SetDefault("redis.port", 6379)
    viper.SetDefault("redis.db", 0)

    viper.SetDefault("jwt.access_token_ttl", "15m")
    viper.SetDefault("jwt.refresh_token_ttl", "24h")

    viper.SetDefault("metrics.enabled", true)
    viper.SetDefault("metrics.port", "9090")

    viper.SetDefault("tracing.enabled", true)
}
```

## 2. Enterprise HTTP Server with Gin Framework

Now let's build an enterprise-grade HTTP server using the Gin framework with proper middleware, error handling, and observability.

### Enterprise HTTP Server Foundation

```go
// pkg/server/server.go
package server

import (
    "context"
    "fmt"
    "net/http"
    "os"
    "os/signal"
    "syscall"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/prometheus/client_golang/prometheus/promhttp"
    "github.com/sirupsen/logrus"

    "github.com/yourusername/enterprise-microservices/pkg/config"
    "github.com/yourusername/enterprise-microservices/pkg/middleware"
    "github.com/yourusername/enterprise-microservices/pkg/metrics"
)

type Server struct {
    config *config.Config
    router *gin.Engine
    logger *logrus.Logger
}

func New(cfg *config.Config, logger *logrus.Logger) *Server {
    // Set Gin mode based on environment
    if cfg.Server.Host != "localhost" {
        gin.SetMode(gin.ReleaseMode)
    }

    router := gin.New()

    return &Server{
        config: cfg,
        router: router,
        logger: logger,
    }
}

func (s *Server) SetupMiddleware() {
    // Recovery middleware
    s.router.Use(gin.Recovery())

    // CORS middleware
    s.router.Use(middleware.CORS())

    // Request ID middleware
    s.router.Use(middleware.RequestID())

    // Logging middleware
    s.router.Use(middleware.Logger(s.logger))

    // Metrics middleware
    s.router.Use(middleware.Metrics())

    // Rate limiting middleware
    s.router.Use(middleware.RateLimit())
}

func (s *Server) SetupRoutes() {
    // Health check endpoints
    health := s.router.Group("/health")
    {
        health.GET("/live", s.livenessProbe)
        health.GET("/ready", s.readinessProbe)
    }

    // Metrics endpoint
    s.router.GET("/metrics", gin.WrapH(promhttp.Handler()))

    // API versioning
    v1 := s.router.Group("/api/v1")
    {
        // User routes will be added here
        v1.GET("/users", s.placeholder)
    }
}

func (s *Server) livenessProbe(c *gin.Context) {
    c.JSON(http.StatusOK, gin.H{
        "status":    "alive",
        "timestamp": time.Now().UTC(),
        "service":   s.config.Tracing.ServiceName,
    })
}

func (s *Server) readinessProbe(c *gin.Context) {
    // Add database connectivity check here
    c.JSON(http.StatusOK, gin.H{
        "status":    "ready",
        "timestamp": time.Now().UTC(),
        "service":   s.config.Tracing.ServiceName,
        "checks": gin.H{
            "database": "healthy",
            "redis":    "healthy",
        },
    })
}

func (s *Server) placeholder(c *gin.Context) {
    c.JSON(http.StatusOK, gin.H{
        "message": "User service endpoint - implementation coming soon",
    })
}
```

<Callout type="important">
  This enterprise server foundation includes proper middleware stack, health
  checks, metrics exposure, and structured logging - all essential for
  production microservices.
</Callout>

### Enterprise Middleware Implementation

Let's implement the middleware components that provide cross-cutting concerns:

```go
// pkg/middleware/middleware.go
package middleware

import (
    "strconv"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/google/uuid"
    "github.com/prometheus/client_golang/prometheus"
    "github.com/sirupsen/logrus"
    "golang.org/x/time/rate"
)

var (
    httpRequestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status_code"},
    )

    httpRequestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "Duration of HTTP requests",
        },
        []string{"method", "endpoint"},
    )
)

func init() {
    prometheus.MustRegister(httpRequestsTotal)
    prometheus.MustRegister(httpRequestDuration)
}

// CORS middleware
func CORS() gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        c.Header("Access-Control-Allow-Origin", "*")
        c.Header("Access-Control-Allow-Credentials", "true")
        c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
        c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

        if c.Request.Method == "OPTIONS" {
            c.AbortWithStatus(204)
            return
        }

        c.Next()
    })
}

// RequestID middleware adds a unique request ID to each request
func RequestID() gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        requestID := c.GetHeader("X-Request-ID")
        if requestID == "" {
            requestID = uuid.New().String()
        }

        c.Header("X-Request-ID", requestID)
        c.Set("RequestID", requestID)
        c.Next()
    })
}

// Logger middleware with structured logging
func Logger(logger *logrus.Logger) gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        start := time.Now()
        path := c.Request.URL.Path
        raw := c.Request.URL.RawQuery

        c.Next()

        latency := time.Since(start)
        clientIP := c.ClientIP()
        method := c.Request.Method
        statusCode := c.Writer.Status()

        if raw != "" {
            path = path + "?" + raw
        }

        logger.WithFields(logrus.Fields{
            "status_code":  statusCode,
            "latency":      latency,
            "client_ip":    clientIP,
            "method":       method,
            "path":         path,
            "request_id":   c.GetString("RequestID"),
            "user_agent":   c.Request.UserAgent(),
        }).Info("HTTP Request")
    })
}

// Metrics middleware
func Metrics() gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        start := time.Now()

        c.Next()

        duration := time.Since(start).Seconds()
        statusCode := strconv.Itoa(c.Writer.Status())

        httpRequestsTotal.WithLabelValues(
            c.Request.Method,
            c.FullPath(),
            statusCode,
        ).Inc()

        httpRequestDuration.WithLabelValues(
            c.Request.Method,
            c.FullPath(),
        ).Observe(duration)
    })
}

// RateLimit middleware
func RateLimit() gin.HandlerFunc {
    limiter := rate.NewLimiter(rate.Limit(100), 200) // 100 requests per second, burst of 200

    return gin.HandlerFunc(func(c *gin.Context) {
        if !limiter.Allow() {
            c.JSON(429, gin.H{
                "error": "Rate limit exceeded",
                "retry_after": "1s",
            })
            c.Abort()
            return
        }
        c.Next()
    })
}
```

## 3. Domain-Driven Design Architecture

Let's implement a proper domain-driven design structure for our user service:

### Domain Models

```go
// internal/user/model/user.go
package model

import (
    "time"
    "github.com/google/uuid"
    "gorm.io/gorm"
)

type User struct {
    ID        uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
    Email     string         `json:"email" gorm:"uniqueIndex;not null" validate:"required,email"`
    FirstName string         `json:"first_name" gorm:"not null" validate:"required,min=2,max=50"`
    LastName  string         `json:"last_name" gorm:"not null" validate:"required,min=2,max=50"`
    Password  string         `json:"-" gorm:"not null"`
    Role      UserRole       `json:"role" gorm:"default:'user'"`
    Status    UserStatus     `json:"status" gorm:"default:'active'"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

type UserRole string

const (
    RoleUser  UserRole = "user"
    RoleAdmin UserRole = "admin"
)

type UserStatus string

const (
    StatusActive   UserStatus = "active"
    StatusInactive UserStatus = "inactive"
    StatusSuspended UserStatus = "suspended"
)

type CreateUserRequest struct {
    Email     string `json:"email" validate:"required,email"`
    FirstName string `json:"first_name" validate:"required,min=2,max=50"`
    LastName  string `json:"last_name" validate:"required,min=2,max=50"`
    Password  string `json:"password" validate:"required,min=8"`
}

type UpdateUserRequest struct {
    FirstName *string     `json:"first_name,omitempty" validate:"omitempty,min=2,max=50"`
    LastName  *string     `json:"last_name,omitempty" validate:"omitempty,min=2,max=50"`
    Role      *UserRole   `json:"role,omitempty"`
    Status    *UserStatus `json:"status,omitempty"`
}

type UserResponse struct {
    ID        uuid.UUID  `json:"id"`
    Email     string     `json:"email"`
    FirstName string     `json:"first_name"`
    LastName  string     `json:"last_name"`
    Role      UserRole   `json:"role"`
    Status    UserStatus `json:"status"`
    CreatedAt time.Time  `json:"created_at"`
    UpdatedAt time.Time  `json:"updated_at"`
}

func (u *User) ToResponse() *UserResponse {
    return &UserResponse{
        ID:        u.ID,
        Email:     u.Email,
        FirstName: u.FirstName,
        LastName:  u.LastName,
        Role:      u.Role,
        Status:    u.Status,
        CreatedAt: u.CreatedAt,
        UpdatedAt: u.UpdatedAt,
    }
}
```

### Repository Layer

```go
// internal/user/repository/user_repository.go
package repository

import (
    "context"
    "errors"

    "github.com/google/uuid"
    "gorm.io/gorm"

    "github.com/yourusername/enterprise-microservices/internal/user/model"
)

type UserRepository interface {
    Create(ctx context.Context, user *model.User) error
    GetByID(ctx context.Context, id uuid.UUID) (*model.User, error)
    GetByEmail(ctx context.Context, email string) (*model.User, error)
    Update(ctx context.Context, user *model.User) error
    Delete(ctx context.Context, id uuid.UUID) error
    List(ctx context.Context, limit, offset int) ([]*model.User, int64, error)
}

type userRepository struct {
    db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
    return &userRepository{db: db}
}

func (r *userRepository) Create(ctx context.Context, user *model.User) error {
    return r.db.WithContext(ctx).Create(user).Error
}

func (r *userRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
    var user model.User
    err := r.db.WithContext(ctx).Where("id = ?", id).First(&user).Error
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, errors.New("user not found")
        }
        return nil, err
    }
    return &user, nil
}

func (r *userRepository) GetByEmail(ctx context.Context, email string) (*model.User, error) {
    var user model.User
    err := r.db.WithContext(ctx).Where("email = ?", email).First(&user).Error
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, errors.New("user not found")
        }
        return nil, err
    }
    return &user, nil
}

func (r *userRepository) Update(ctx context.Context, user *model.User) error {
    return r.db.WithContext(ctx).Save(user).Error
}

func (r *userRepository) Delete(ctx context.Context, id uuid.UUID) error {
    return r.db.WithContext(ctx).Delete(&model.User{}, id).Error
}

func (r *userRepository) List(ctx context.Context, limit, offset int) ([]*model.User, int64, error) {
    var users []*model.User
    var total int64

    if err := r.db.WithContext(ctx).Model(&model.User{}).Count(&total).Error; err != nil {
        return nil, 0, err
    }

    err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Find(&users).Error
    return users, total, err
}
```

### Service Layer with Business Logic

```go
// internal/user/service/user_service.go
package service

import (
    "context"
    "errors"
    "time"

    "github.com/google/uuid"
    "golang.org/x/crypto/bcrypt"

    "github.com/yourusername/enterprise-microservices/internal/user/model"
    "github.com/yourusername/enterprise-microservices/internal/user/repository"
    "github.com/yourusername/enterprise-microservices/pkg/logger"
)

type UserService interface {
    CreateUser(ctx context.Context, req *model.CreateUserRequest) (*model.UserResponse, error)
    GetUser(ctx context.Context, id uuid.UUID) (*model.UserResponse, error)
    UpdateUser(ctx context.Context, id uuid.UUID, req *model.UpdateUserRequest) (*model.UserResponse, error)
    DeleteUser(ctx context.Context, id uuid.UUID) error
    ListUsers(ctx context.Context, limit, offset int) ([]*model.UserResponse, int64, error)
    GetUserByEmail(ctx context.Context, email string) (*model.User, error)
}

type userService struct {
    repo   repository.UserRepository
    logger logger.Logger
}

func NewUserService(repo repository.UserRepository, logger logger.Logger) UserService {
    return &userService{
        repo:   repo,
        logger: logger,
    }
}

func (s *userService) CreateUser(ctx context.Context, req *model.CreateUserRequest) (*model.UserResponse, error) {
    // Check if user already exists
    existingUser, _ := s.repo.GetByEmail(ctx, req.Email)
    if existingUser != nil {
        return nil, errors.New("user with this email already exists")
    }

    // Hash password
    hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
    if err != nil {
        s.logger.Error("Failed to hash password", "error", err)
        return nil, errors.New("failed to process password")
    }

    user := &model.User{
        Email:     req.Email,
        FirstName: req.FirstName,
        LastName:  req.LastName,
        Password:  string(hashedPassword),
        Role:      model.RoleUser,
        Status:    model.StatusActive,
        CreatedAt: time.Now(),
        UpdatedAt: time.Now(),
    }

    if err := s.repo.Create(ctx, user); err != nil {
        s.logger.Error("Failed to create user", "error", err, "email", req.Email)
        return nil, errors.New("failed to create user")
    }

    s.logger.Info("User created successfully", "user_id", user.ID, "email", user.Email)
    return user.ToResponse(), nil
}

func (s *userService) GetUser(ctx context.Context, id uuid.UUID) (*model.UserResponse, error) {
    user, err := s.repo.GetByID(ctx, id)
    if err != nil {
        return nil, err
    }
    return user.ToResponse(), nil
}

func (s *userService) UpdateUser(ctx context.Context, id uuid.UUID, req *model.UpdateUserRequest) (*model.UserResponse, error) {
    user, err := s.repo.GetByID(ctx, id)
    if err != nil {
        return nil, err
    }

    // Update fields if provided
    if req.FirstName != nil {
        user.FirstName = *req.FirstName
    }
    if req.LastName != nil {
        user.LastName = *req.LastName
    }
    if req.Role != nil {
        user.Role = *req.Role
    }
    if req.Status != nil {
        user.Status = *req.Status
    }

    user.UpdatedAt = time.Now()

    if err := s.repo.Update(ctx, user); err != nil {
        s.logger.Error("Failed to update user", "error", err, "user_id", id)
        return nil, errors.New("failed to update user")
    }

    s.logger.Info("User updated successfully", "user_id", user.ID)
    return user.ToResponse(), nil
}

func (s *userService) DeleteUser(ctx context.Context, id uuid.UUID) error {
    if err := s.repo.Delete(ctx, id); err != nil {
        s.logger.Error("Failed to delete user", "error", err, "user_id", id)
        return errors.New("failed to delete user")
    }

    s.logger.Info("User deleted successfully", "user_id", id)
    return nil
}

func (s *userService) ListUsers(ctx context.Context, limit, offset int) ([]*model.UserResponse, int64, error) {
    users, total, err := s.repo.List(ctx, limit, offset)
    if err != nil {
        return nil, 0, err
    }

    responses := make([]*model.UserResponse, len(users))
    for i, user := range users {
        responses[i] = user.ToResponse()
    }

    return responses, total, nil
}

func (s *userService) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
    return s.repo.GetByEmail(ctx, email)
}
```

## 4. HTTP Handlers with Validation

Let's implement enterprise-grade HTTP handlers with proper validation, error handling, and response formatting:

### HTTP Handlers

```go
// internal/user/handler/user_handler.go
package handler

import (
    "net/http"
    "strconv"

    "github.com/gin-gonic/gin"
    "github.com/go-playground/validator/v10"
    "github.com/google/uuid"

    "github.com/yourusername/enterprise-microservices/internal/user/model"
    "github.com/yourusername/enterprise-microservices/internal/user/service"
    "github.com/yourusername/enterprise-microservices/pkg/errors"
    "github.com/yourusername/enterprise-microservices/pkg/logger"
)

type UserHandler struct {
    service   service.UserService
    validator *validator.Validate
    logger    logger.Logger
}

func NewUserHandler(service service.UserService, logger logger.Logger) *UserHandler {
    return &UserHandler{
        service:   service,
        validator: validator.New(),
        logger:    logger,
    }
}

type APIResponse struct {
    Success bool        `json:"success"`
    Data    interface{} `json:"data,omitempty"`
    Error   *APIError   `json:"error,omitempty"`
    Meta    *Meta       `json:"meta,omitempty"`
}

type APIError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

type Meta struct {
    Total  int64 `json:"total,omitempty"`
    Limit  int   `json:"limit,omitempty"`
    Offset int   `json:"offset,omitempty"`
}

func (h *UserHandler) CreateUser(c *gin.Context) {
    var req model.CreateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        h.respondWithError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request body", err.Error())
        return
    }

    if err := h.validator.Struct(&req); err != nil {
        h.respondWithError(c, http.StatusBadRequest, "VALIDATION_ERROR", "Validation failed", err.Error())
        return
    }

    user, err := h.service.CreateUser(c.Request.Context(), &req)
    if err != nil {
        h.logger.Error("Failed to create user", "error", err)
        h.respondWithError(c, http.StatusInternalServerError, "CREATE_USER_ERROR", "Failed to create user", err.Error())
        return
    }

    h.respondWithSuccess(c, http.StatusCreated, user, nil)
}

func (h *UserHandler) GetUser(c *gin.Context) {
    idStr := c.Param("id")
    id, err := uuid.Parse(idStr)
    if err != nil {
        h.respondWithError(c, http.StatusBadRequest, "INVALID_USER_ID", "Invalid user ID format", err.Error())
        return
    }

    user, err := h.service.GetUser(c.Request.Context(), id)
    if err != nil {
        h.respondWithError(c, http.StatusNotFound, "USER_NOT_FOUND", "User not found", err.Error())
        return
    }

    h.respondWithSuccess(c, http.StatusOK, user, nil)
}

func (h *UserHandler) UpdateUser(c *gin.Context) {
    idStr := c.Param("id")
    id, err := uuid.Parse(idStr)
    if err != nil {
        h.respondWithError(c, http.StatusBadRequest, "INVALID_USER_ID", "Invalid user ID format", err.Error())
        return
    }

    var req model.UpdateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        h.respondWithError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request body", err.Error())
        return
    }

    if err := h.validator.Struct(&req); err != nil {
        h.respondWithError(c, http.StatusBadRequest, "VALIDATION_ERROR", "Validation failed", err.Error())
        return
    }

    user, err := h.service.UpdateUser(c.Request.Context(), id, &req)
    if err != nil {
        h.respondWithError(c, http.StatusInternalServerError, "UPDATE_USER_ERROR", "Failed to update user", err.Error())
        return
    }

    h.respondWithSuccess(c, http.StatusOK, user, nil)
}

func (h *UserHandler) DeleteUser(c *gin.Context) {
    idStr := c.Param("id")
    id, err := uuid.Parse(idStr)
    if err != nil {
        h.respondWithError(c, http.StatusBadRequest, "INVALID_USER_ID", "Invalid user ID format", err.Error())
        return
    }

    if err := h.service.DeleteUser(c.Request.Context(), id); err != nil {
        h.respondWithError(c, http.StatusInternalServerError, "DELETE_USER_ERROR", "Failed to delete user", err.Error())
        return
    }

    h.respondWithSuccess(c, http.StatusNoContent, nil, nil)
}

func (h *UserHandler) ListUsers(c *gin.Context) {
    limitStr := c.DefaultQuery("limit", "10")
    offsetStr := c.DefaultQuery("offset", "0")

    limit, err := strconv.Atoi(limitStr)
    if err != nil || limit <= 0 || limit > 100 {
        limit = 10
    }

    offset, err := strconv.Atoi(offsetStr)
    if err != nil || offset < 0 {
        offset = 0
    }

    users, total, err := h.service.ListUsers(c.Request.Context(), limit, offset)
    if err != nil {
        h.respondWithError(c, http.StatusInternalServerError, "LIST_USERS_ERROR", "Failed to list users", err.Error())
        return
    }

    meta := &Meta{
        Total:  total,
        Limit:  limit,
        Offset: offset,
    }

    h.respondWithSuccess(c, http.StatusOK, users, meta)
}

func (h *UserHandler) respondWithSuccess(c *gin.Context, statusCode int, data interface{}, meta *Meta) {
    response := APIResponse{
        Success: true,
        Data:    data,
        Meta:    meta,
    }
    c.JSON(statusCode, response)
}

func (h *UserHandler) respondWithError(c *gin.Context, statusCode int, code, message, details string) {
    response := APIResponse{
        Success: false,
        Error: &APIError{
            Code:    code,
            Message: message,
            Details: details,
        },
    }
    c.JSON(statusCode, response)
}
```

## 5. JWT Authentication Service

Enterprise microservices require robust authentication. Let's implement a complete JWT-based authentication service:

### JWT Service Implementation

```go
// internal/auth/service/jwt_service.go
package service

import (
    "errors"
    "time"

    "github.com/golang-jwt/jwt/v5"
    "github.com/google/uuid"

    "github.com/yourusername/enterprise-microservices/pkg/config"
)

type JWTService interface {
    GenerateTokenPair(userID uuid.UUID, email string, role string) (*TokenPair, error)
    ValidateAccessToken(tokenString string) (*Claims, error)
    ValidateRefreshToken(tokenString string) (*Claims, error)
    RefreshTokens(refreshToken string) (*TokenPair, error)
}

type TokenPair struct {
    AccessToken  string `json:"access_token"`
    RefreshToken string `json:"refresh_token"`
    ExpiresIn    int64  `json:"expires_in"`
}

type Claims struct {
    UserID uuid.UUID `json:"user_id"`
    Email  string    `json:"email"`
    Role   string    `json:"role"`
    Type   string    `json:"type"` // "access" or "refresh"
    jwt.RegisteredClaims
}

type jwtService struct {
    config *config.JWTConfig
}

func NewJWTService(config *config.JWTConfig) JWTService {
    return &jwtService{config: config}
}

func (s *jwtService) GenerateTokenPair(userID uuid.UUID, email string, role string) (*TokenPair, error) {
    now := time.Now()

    // Generate access token
    accessClaims := &Claims{
        UserID: userID,
        Email:  email,
        Role:   role,
        Type:   "access",
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(now.Add(s.config.AccessTokenTTL)),
            IssuedAt:  jwt.NewNumericDate(now),
            NotBefore: jwt.NewNumericDate(now),
            Issuer:    "enterprise-microservices",
            Subject:   userID.String(),
        },
    }

    accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
    accessTokenString, err := accessToken.SignedString([]byte(s.config.Secret))
    if err != nil {
        return nil, err
    }

    // Generate refresh token
    refreshClaims := &Claims{
        UserID: userID,
        Email:  email,
        Role:   role,
        Type:   "refresh",
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(now.Add(s.config.RefreshTokenTTL)),
            IssuedAt:  jwt.NewNumericDate(now),
            NotBefore: jwt.NewNumericDate(now),
            Issuer:    "enterprise-microservices",
            Subject:   userID.String(),
        },
    }

    refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
    refreshTokenString, err := refreshToken.SignedString([]byte(s.config.Secret))
    if err != nil {
        return nil, err
    }

    return &TokenPair{
        AccessToken:  accessTokenString,
        RefreshToken: refreshTokenString,
        ExpiresIn:    int64(s.config.AccessTokenTTL.Seconds()),
    }, nil
}

func (s *jwtService) ValidateAccessToken(tokenString string) (*Claims, error) {
    return s.validateToken(tokenString, "access")
}

func (s *jwtService) ValidateRefreshToken(tokenString string) (*Claims, error) {
    return s.validateToken(tokenString, "refresh")
}

func (s *jwtService) validateToken(tokenString, expectedType string) (*Claims, error) {
    token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
        if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
            return nil, errors.New("unexpected signing method")
        }
        return []byte(s.config.Secret), nil
    })

    if err != nil {
        return nil, err
    }

    claims, ok := token.Claims.(*Claims)
    if !ok || !token.Valid {
        return nil, errors.New("invalid token")
    }

    if claims.Type != expectedType {
        return nil, errors.New("invalid token type")
    }

    return claims, nil
}

func (s *jwtService) RefreshTokens(refreshToken string) (*TokenPair, error) {
    claims, err := s.ValidateRefreshToken(refreshToken)
    if err != nil {
        return nil, err
    }

    return s.GenerateTokenPair(claims.UserID, claims.Email, claims.Role)
}
```

### Authentication Middleware

```go
// pkg/middleware/auth.go
package middleware

import (
    "net/http"
    "strings"

    "github.com/gin-gonic/gin"

    "github.com/yourusername/enterprise-microservices/internal/auth/service"
)

func JWTAuth(jwtService service.JWTService) gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "Authorization header required",
            })
            c.Abort()
            return
        }

        tokenParts := strings.Split(authHeader, " ")
        if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "Invalid authorization header format",
            })
            c.Abort()
            return
        }

        claims, err := jwtService.ValidateAccessToken(tokenParts[1])
        if err != nil {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "Invalid or expired token",
            })
            c.Abort()
            return
        }

        // Set user context
        c.Set("user_id", claims.UserID)
        c.Set("user_email", claims.Email)
        c.Set("user_role", claims.Role)

        c.Next()
    })
}

func RequireRole(role string) gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        userRole, exists := c.Get("user_role")
        if !exists {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "User role not found in context",
            })
            c.Abort()
            return
        }

        if userRole != role && userRole != "admin" {
            c.JSON(http.StatusForbidden, gin.H{
                "error": "Insufficient permissions",
            })
            c.Abort()
            return
        }

        c.Next()
    })
}
```

## 6. Database Management & Connection Pooling

Let's implement enterprise-grade database management with connection pooling, migrations, and health checks:

### Database Connection Manager

```go
// pkg/database/postgres.go
package database

import (
    "context"
    "fmt"
    "time"

    "gorm.io/driver/postgres"
    "gorm.io/gorm"
    "gorm.io/gorm/logger"

    "github.com/yourusername/enterprise-microservices/pkg/config"
    pkglogger "github.com/yourusername/enterprise-microservices/pkg/logger"
)

type Database struct {
    DB     *gorm.DB
    config *config.DatabaseConfig
    logger pkglogger.Logger
}

func NewPostgresConnection(cfg *config.DatabaseConfig, logger pkglogger.Logger) (*Database, error) {
    dsn := fmt.Sprintf(
        "host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
        cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.DBName, cfg.SSLMode,
    )

    // Configure GORM logger
    gormLogger := logger.LogMode(logger.Info)
    if cfg.Host != "localhost" {
        gormLogger = logger.LogMode(logger.Error)
    }

    db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
        Logger: gormLogger,
        NowFunc: func() time.Time {
            return time.Now().UTC()
        },
    })
    if err != nil {
        return nil, fmt.Errorf("failed to connect to database: %w", err)
    }

    // Get underlying sql.DB for connection pool configuration
    sqlDB, err := db.DB()
    if err != nil {
        return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
    }

    // Configure connection pool
    sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
    sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
    sqlDB.SetConnMaxLifetime(time.Hour)

    // Test connection
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    if err := sqlDB.PingContext(ctx); err != nil {
        return nil, fmt.Errorf("failed to ping database: %w", err)
    }

    logger.Info("Database connection established successfully")

    return &Database{
        DB:     db,
        config: cfg,
        logger: logger,
    }, nil
}

func (d *Database) Close() error {
    sqlDB, err := d.DB.DB()
    if err != nil {
        return err
    }
    return sqlDB.Close()
}

func (d *Database) HealthCheck(ctx context.Context) error {
    sqlDB, err := d.DB.DB()
    if err != nil {
        return err
    }
    return sqlDB.PingContext(ctx)
}
```

<Callout type="success">
  This database setup includes connection pooling, health checks, and proper
  error handling - all essential for enterprise applications.
</Callout>

## 7. Circuit Breaker & Resilience Patterns

Enterprise microservices must be resilient to failures. Let's implement circuit breakers, retries, and timeouts:

### Circuit Breaker Implementation

```go
// pkg/resilience/circuit_breaker.go
package resilience

import (
    "context"
    "errors"
    "sync"
    "time"
)

type State int

const (
    StateClosed State = iota
    StateHalfOpen
    StateOpen
)

type CircuitBreaker struct {
    name           string
    maxRequests    uint32
    interval       time.Duration
    timeout        time.Duration
    readyToTrip    func(counts Counts) bool
    onStateChange  func(name string, from State, to State)

    mutex      sync.Mutex
    state      State
    generation uint64
    counts     Counts
    expiry     time.Time
}

type Counts struct {
    Requests             uint32
    TotalSuccesses       uint32
    TotalFailures        uint32
    ConsecutiveSuccesses uint32
    ConsecutiveFailures  uint32
}

func NewCircuitBreaker(name string, settings Settings) *CircuitBreaker {
    cb := &CircuitBreaker{
        name:           name,
        maxRequests:    settings.MaxRequests,
        interval:       settings.Interval,
        timeout:        settings.Timeout,
        readyToTrip:    settings.ReadyToTrip,
        onStateChange:  settings.OnStateChange,
    }

    cb.toNewGeneration(time.Now())
    return cb
}

type Settings struct {
    Name          string
    MaxRequests   uint32
    Interval      time.Duration
    Timeout       time.Duration
    ReadyToTrip   func(counts Counts) bool
    OnStateChange func(name string, from State, to State)
}

func (cb *CircuitBreaker) Execute(ctx context.Context, req func() (interface{}, error)) (interface{}, error) {
    generation, err := cb.beforeRequest()
    if err != nil {
        return nil, err
    }

    defer func() {
        e := recover()
        if e != nil {
            cb.afterRequest(generation, false)
            panic(e)
        }
    }()

    result, err := req()
    cb.afterRequest(generation, err == nil)
    return result, err
}

func (cb *CircuitBreaker) beforeRequest() (uint64, error) {
    cb.mutex.Lock()
    defer cb.mutex.Unlock()

    now := time.Now()
    state, generation := cb.currentState(now)

    if state == StateOpen {
        return generation, errors.New("circuit breaker is open")
    } else if state == StateHalfOpen && cb.counts.Requests >= cb.maxRequests {
        return generation, errors.New("too many requests")
    }

    cb.counts.onRequest()
    return generation, nil
}

func (cb *CircuitBreaker) afterRequest(before uint64, success bool) {
    cb.mutex.Lock()
    defer cb.mutex.Unlock()

    now := time.Now()
    state, generation := cb.currentState(now)
    if generation != before {
        return
    }

    if success {
        cb.onSuccess(state, now)
    } else {
        cb.onFailure(state, now)
    }
}

func (cb *CircuitBreaker) onSuccess(state State, now time.Time) {
    cb.counts.onSuccess()

    if state == StateHalfOpen && cb.counts.ConsecutiveSuccesses >= cb.maxRequests {
        cb.setState(StateClosed, now)
    }
}

func (cb *CircuitBreaker) onFailure(state State, now time.Time) {
    cb.counts.onFailure()

    if cb.readyToTrip(cb.counts) {
        cb.setState(StateOpen, now)
    }
}

func (cb *CircuitBreaker) currentState(now time.Time) (State, uint64) {
    switch cb.state {
    case StateClosed:
        if !cb.expiry.IsZero() && cb.expiry.Before(now) {
            cb.toNewGeneration(now)
        }
    case StateOpen:
        if cb.expiry.Before(now) {
            cb.setState(StateHalfOpen, now)
        }
    }
    return cb.state, cb.generation
}

func (cb *CircuitBreaker) setState(state State, now time.Time) {
    if cb.state == state {
        return
    }

    prev := cb.state
    cb.state = state
    cb.toNewGeneration(now)

    if cb.onStateChange != nil {
        cb.onStateChange(cb.name, prev, state)
    }
}

func (cb *CircuitBreaker) toNewGeneration(now time.Time) {
    cb.generation++
    cb.counts.clear()

    var zero time.Time
    switch cb.state {
    case StateClosed:
        if cb.interval == 0 {
            cb.expiry = zero
        } else {
            cb.expiry = now.Add(cb.interval)
        }
    case StateOpen:
        cb.expiry = now.Add(cb.timeout)
    default: // StateHalfOpen
        cb.expiry = zero
    }
}

func (c *Counts) onRequest() {
    c.Requests++
}

func (c *Counts) onSuccess() {
    c.TotalSuccesses++
    c.ConsecutiveSuccesses++
    c.ConsecutiveFailures = 0
}

func (c *Counts) onFailure() {
    c.TotalFailures++
    c.ConsecutiveFailures++
    c.ConsecutiveSuccesses = 0
}

func (c *Counts) clear() {
    c.Requests = 0
    c.TotalSuccesses = 0
    c.TotalFailures = 0
    c.ConsecutiveSuccesses = 0
    c.ConsecutiveFailures = 0
}
```

### Retry Mechanism

```go
// pkg/resilience/retry.go
package resilience

import (
    "context"
    "math"
    "time"
)

type RetryConfig struct {
    MaxAttempts int
    InitialDelay time.Duration
    MaxDelay     time.Duration
    Multiplier   float64
    Jitter       bool
}

func WithRetry(ctx context.Context, config RetryConfig, operation func() error) error {
    var lastErr error

    for attempt := 0; attempt < config.MaxAttempts; attempt++ {
        if attempt > 0 {
            delay := calculateDelay(config, attempt)
            select {
            case <-time.After(delay):
            case <-ctx.Done():
                return ctx.Err()
            }
        }

        if err := operation(); err != nil {
            lastErr = err
            continue
        }

        return nil
    }

    return lastErr
}

func calculateDelay(config RetryConfig, attempt int) time.Duration {
    delay := float64(config.InitialDelay) * math.Pow(config.Multiplier, float64(attempt-1))

    if delay > float64(config.MaxDelay) {
        delay = float64(config.MaxDelay)
    }

    if config.Jitter {
        delay = delay * (0.5 + 0.5*time.Now().UnixNano()%1000/1000.0)
    }

    return time.Duration(delay)
}
```

## 8. Event-Driven Architecture with Message Queues

Enterprise microservices often need asynchronous communication. Let's implement event-driven architecture:

### Event System with Redis Streams

```go
// pkg/events/event.go
package events

import (
    "context"
    "encoding/json"
    "errors"
    "time"

    "github.com/google/uuid"
    "github.com/redis/go-redis/v9"

    "github.com/yourusername/enterprise-microservices/pkg/logger"
)

type Event struct {
    ID        string                 `json:"id"`
    Type      string                 `json:"type"`
    Source    string                 `json:"source"`
    Data      map[string]interface{} `json:"data"`
    Timestamp time.Time              `json:"timestamp"`
    Version   string                 `json:"version"`
}

func NewEvent(eventType, source string, data map[string]interface{}) *Event {
    return &Event{
        ID:        uuid.New().String(),
        Type:      eventType,
        Source:    source,
        Data:      data,
        Timestamp: time.Now().UTC(),
        Version:   "1.0",
    }
}

type Publisher interface {
    Publish(ctx context.Context, topic string, event *Event) error
    PublishBatch(ctx context.Context, topic string, events []*Event) error
}

type Subscriber interface {
    Subscribe(ctx context.Context, topic string, handler EventHandler) error
}

type EventHandler func(ctx context.Context, event *Event) error

// Redis-based event bus with reliability features
type RedisEventBus struct {
    client redis.UniversalClient
    logger logger.Logger
}

func NewRedisEventBus(client redis.UniversalClient, logger logger.Logger) *RedisEventBus {
    return &RedisEventBus{
        client: client,
        logger: logger,
    }
}

func (r *RedisEventBus) Publish(ctx context.Context, topic string, event *Event) error {
    data, err := json.Marshal(event)
    if err != nil {
        r.logger.Error("Failed to marshal event", "error", err, "event_id", event.ID)
        return err
    }

    // Use Redis Streams for better reliability
    _, err = r.client.XAdd(ctx, &redis.XAddArgs{
        Stream: topic,
        Values: map[string]interface{}{
            "event": string(data),
        },
    }).Result()

    if err != nil {
        r.logger.Error("Failed to publish event", "error", err, "topic", topic, "event_id", event.ID)
        return err
    }

    r.logger.Debug("Event published successfully", "topic", topic, "event_id", event.ID, "event_type", event.Type)
    return nil
}

func (r *RedisEventBus) Subscribe(ctx context.Context, topic string, handler EventHandler) error {
    consumerGroup := "microservice-group"
    consumerName := uuid.New().String()

    // Create consumer group if it doesn't exist
    r.client.XGroupCreateMkStream(ctx, topic, consumerGroup, "0")

    for {
        select {
        case <-ctx.Done():
            return ctx.Err()
        default:
            streams, err := r.client.XReadGroup(ctx, &redis.XReadGroupArgs{
                Group:    consumerGroup,
                Consumer: consumerName,
                Streams:  []string{topic, ">"},
                Count:    10,
                Block:    time.Second,
            }).Result()

            if err != nil {
                if err == redis.Nil {
                    continue
                }
                r.logger.Error("Failed to read from stream", "error", err, "topic", topic)
                time.Sleep(time.Second)
                continue
            }

            for _, stream := range streams {
                for _, message := range stream.Messages {
                    if err := r.processMessage(ctx, topic, consumerGroup, message, handler); err != nil {
                        r.logger.Error("Failed to process message", "error", err, "message_id", message.ID)
                    }
                }
            }
        }
    }
}

func (r *RedisEventBus) processMessage(ctx context.Context, topic, group string, message redis.XMessage, handler EventHandler) error {
    eventData, ok := message.Values["event"].(string)
    if !ok {
        return errors.New("invalid event data format")
    }

    var event Event
    if err := json.Unmarshal([]byte(eventData), &event); err != nil {
        r.logger.Error("Failed to unmarshal event", "error", err, "message_id", message.ID)
        // Acknowledge the message even if we can't process it to avoid infinite retries
        r.client.XAck(ctx, topic, group, message.ID)
        return err
    }

    // Process the event
    if err := handler(ctx, &event); err != nil {
        r.logger.Error("Event handler failed", "error", err, "event_id", event.ID, "message_id", message.ID)
        return err
    }

    // Acknowledge successful processing
    r.client.XAck(ctx, topic, group, message.ID)
    r.logger.Debug("Event processed successfully", "event_id", event.ID, "message_id", message.ID)

    return nil
}
```

## 9. Distributed Tracing with OpenTelemetry

Enterprise microservices need distributed tracing for observability across service boundaries:

### OpenTelemetry Setup

```go
// pkg/tracing/tracer.go
package tracing

import (
    "context"
    "fmt"
    "time"

    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/exporters/jaeger"
    "go.opentelemetry.io/otel/propagation"
    "go.opentelemetry.io/otel/sdk/resource"
    "go.opentelemetry.io/otel/sdk/trace"
    semconv "go.opentelemetry.io/otel/semconv/v1.17.0"

    "github.com/yourusername/enterprise-microservices/pkg/config"
    "github.com/yourusername/enterprise-microservices/pkg/logger"
)

type TracerProvider struct {
    provider *trace.TracerProvider
    logger   logger.Logger
}

func NewTracerProvider(cfg *config.TracingConfig, logger logger.Logger) (*TracerProvider, error) {
    if !cfg.Enabled {
        logger.Info("Tracing is disabled")
        return &TracerProvider{logger: logger}, nil
    }

    // Create Jaeger exporter
    exp, err := jaeger.New(jaeger.WithCollectorEndpoint(jaeger.WithEndpoint(cfg.Endpoint)))
    if err != nil {
        return nil, fmt.Errorf("failed to create Jaeger exporter: %w", err)
    }

    // Create resource
    res, err := resource.Merge(
        resource.Default(),
        resource.NewWithAttributes(
            semconv.SchemaURL,
            semconv.ServiceName(cfg.ServiceName),
            semconv.ServiceVersion("1.0.0"),
            attribute.String("environment", "production"),
        ),
    )
    if err != nil {
        return nil, fmt.Errorf("failed to create resource: %w", err)
    }

    // Create tracer provider
    tp := trace.NewTracerProvider(
        trace.WithBatcher(exp),
        trace.WithResource(res),
        trace.WithSampler(trace.AlwaysSample()),
    )

    // Set global tracer provider
    otel.SetTracerProvider(tp)

    // Set global propagator
    otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
        propagation.TraceContext{},
        propagation.Baggage{},
    ))

    logger.Info("Distributed tracing initialized", "service", cfg.ServiceName, "endpoint", cfg.Endpoint)

    return &TracerProvider{
        provider: tp,
        logger:   logger,
    }, nil
}

func (tp *TracerProvider) Shutdown(ctx context.Context) error {
    if tp.provider == nil {
        return nil
    }

    shutdownCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()

    return tp.provider.Shutdown(shutdownCtx)
}

func (tp *TracerProvider) GetTracer(name string) trace.Tracer {
    if tp.provider == nil {
        return otel.Tracer(name)
    }
    return tp.provider.Tracer(name)
}
```

### Tracing Middleware

```go
// pkg/middleware/tracing.go
package middleware

import (
    "github.com/gin-gonic/gin"
    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/propagation"
    "go.opentelemetry.io/otel/trace"
)

func Tracing(serviceName string) gin.HandlerFunc {
    tracer := otel.Tracer(serviceName)

    return gin.HandlerFunc(func(c *gin.Context) {
        // Extract trace context from headers
        ctx := otel.GetTextMapPropagator().Extract(c.Request.Context(), propagation.HeaderCarrier(c.Request.Header))

        // Start span
        ctx, span := tracer.Start(ctx, c.Request.Method+" "+c.FullPath())
        defer span.End()

        // Set span attributes
        span.SetAttributes(
            attribute.String("http.method", c.Request.Method),
            attribute.String("http.url", c.Request.URL.String()),
            attribute.String("http.route", c.FullPath()),
            attribute.String("http.user_agent", c.Request.UserAgent()),
            attribute.String("http.remote_addr", c.ClientIP()),
        )

        // Add trace context to gin context
        c.Request = c.Request.WithContext(ctx)
        c.Set("trace_id", span.SpanContext().TraceID().String())
        c.Set("span_id", span.SpanContext().SpanID().String())

        // Process request
        c.Next()

        // Set response attributes
        span.SetAttributes(
            attribute.Int("http.status_code", c.Writer.Status()),
            attribute.Int("http.response_size", c.Writer.Size()),
        )

        // Set span status based on HTTP status code
        if c.Writer.Status() >= 400 {
            span.SetAttributes(attribute.Bool("error", true))
            if len(c.Errors) > 0 {
                span.SetAttributes(attribute.String("error.message", c.Errors.String()))
            }
        }
    })
}
```

## 10. Comprehensive Testing Strategy

Enterprise applications require thorough testing at multiple levels:

### Unit Testing with Testify

```go
// internal/user/service/user_service_test.go
package service

import (
    "context"
    "errors"
    "testing"
    "time"

    "github.com/google/uuid"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/stretchr/testify/suite"
    "golang.org/x/crypto/bcrypt"

    "github.com/yourusername/enterprise-microservices/internal/user/model"
    "github.com/yourusername/enterprise-microservices/pkg/logger"
)

// Mock repository
type MockUserRepository struct {
    mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, user *model.User) error {
    args := m.Called(ctx, user)
    return args.Error(0)
}

func (m *MockUserRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
    args := m.Called(ctx, id)
    return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email string) (*model.User, error) {
    args := m.Called(ctx, email)
    if args.Get(0) == nil {
        return nil, args.Error(1)
    }
    return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) Update(ctx context.Context, user *model.User) error {
    args := m.Called(ctx, user)
    return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, id uuid.UUID) error {
    args := m.Called(ctx, id)
    return args.Error(0)
}

func (m *MockUserRepository) List(ctx context.Context, limit, offset int) ([]*model.User, int64, error) {
    args := m.Called(ctx, limit, offset)
    return args.Get(0).([]*model.User), args.Get(1).(int64), args.Error(2)
}

// Test suite
type UserServiceTestSuite struct {
    suite.Suite
    service    UserService
    repository *MockUserRepository
    logger     logger.Logger
}

func (suite *UserServiceTestSuite) SetupTest() {
    suite.repository = new(MockUserRepository)
    suite.logger = logger.NewNoop() // No-op logger for tests
    suite.service = NewUserService(suite.repository, suite.logger)
}

func (suite *UserServiceTestSuite) TestCreateUser_Success() {
    // Arrange
    ctx := context.Background()
    req := &model.CreateUserRequest{
        Email:     "<EMAIL>",
        FirstName: "John",
        LastName:  "Doe",
        Password:  "password123",
    }

    suite.repository.On("GetByEmail", ctx, req.Email).Return(nil, errors.New("user not found"))
    suite.repository.On("Create", ctx, mock.AnythingOfType("*model.User")).Return(nil)

    // Act
    result, err := suite.service.CreateUser(ctx, req)

    // Assert
    assert.NoError(suite.T(), err)
    assert.NotNil(suite.T(), result)
    assert.Equal(suite.T(), req.Email, result.Email)
    assert.Equal(suite.T(), req.FirstName, result.FirstName)
    assert.Equal(suite.T(), req.LastName, result.LastName)
    assert.Equal(suite.T(), model.RoleUser, result.Role)
    assert.Equal(suite.T(), model.StatusActive, result.Status)

    suite.repository.AssertExpectations(suite.T())
}

func (suite *UserServiceTestSuite) TestCreateUser_UserAlreadyExists() {
    // Arrange
    ctx := context.Background()
    req := &model.CreateUserRequest{
        Email:     "<EMAIL>",
        FirstName: "John",
        LastName:  "Doe",
        Password:  "password123",
    }

    existingUser := &model.User{
        ID:    uuid.New(),
        Email: req.Email,
    }

    suite.repository.On("GetByEmail", ctx, req.Email).Return(existingUser, nil)

    // Act
    result, err := suite.service.CreateUser(ctx, req)

    // Assert
    assert.Error(suite.T(), err)
    assert.Nil(suite.T(), result)
    assert.Contains(suite.T(), err.Error(), "already exists")

    suite.repository.AssertExpectations(suite.T())
}

func (suite *UserServiceTestSuite) TestGetUser_Success() {
    // Arrange
    ctx := context.Background()
    userID := uuid.New()
    user := &model.User{
        ID:        userID,
        Email:     "<EMAIL>",
        FirstName: "John",
        LastName:  "Doe",
        Role:      model.RoleUser,
        Status:    model.StatusActive,
        CreatedAt: time.Now(),
        UpdatedAt: time.Now(),
    }

    suite.repository.On("GetByID", ctx, userID).Return(user, nil)

    // Act
    result, err := suite.service.GetUser(ctx, userID)

    // Assert
    assert.NoError(suite.T(), err)
    assert.NotNil(suite.T(), result)
    assert.Equal(suite.T(), user.ID, result.ID)
    assert.Equal(suite.T(), user.Email, result.Email)

    suite.repository.AssertExpectations(suite.T())
}

func (suite *UserServiceTestSuite) TestGetUser_NotFound() {
    // Arrange
    ctx := context.Background()
    userID := uuid.New()

    suite.repository.On("GetByID", ctx, userID).Return((*model.User)(nil), errors.New("user not found"))

    // Act
    result, err := suite.service.GetUser(ctx, userID)

    // Assert
    assert.Error(suite.T(), err)
    assert.Nil(suite.T(), result)

    suite.repository.AssertExpectations(suite.T())
}

func TestUserServiceTestSuite(t *testing.T) {
    suite.Run(t, new(UserServiceTestSuite))
}
```

### Integration Testing

```go
// tests/integration/user_integration_test.go
package integration

import (
    "bytes"
    "context"
    "encoding/json"
    "fmt"
    "net/http"
    "net/http/httptest"
    "testing"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    "github.com/testcontainers/testcontainers-go"
    "github.com/testcontainers/testcontainers-go/wait"
    "gorm.io/driver/postgres"
    "gorm.io/gorm"

    "github.com/yourusername/enterprise-microservices/internal/user/handler"
    "github.com/yourusername/enterprise-microservices/internal/user/model"
    "github.com/yourusername/enterprise-microservices/internal/user/repository"
    "github.com/yourusername/enterprise-microservices/internal/user/service"
    "github.com/yourusername/enterprise-microservices/pkg/config"
    "github.com/yourusername/enterprise-microservices/pkg/database"
    "github.com/yourusername/enterprise-microservices/pkg/logger"
)

type UserIntegrationTestSuite struct {
    suite.Suite
    db        *gorm.DB
    container testcontainers.Container
    handler   *handler.UserHandler
    router    *gin.Engine
}

func (suite *UserIntegrationTestSuite) SetupSuite() {
    ctx := context.Background()

    // Start PostgreSQL container
    req := testcontainers.ContainerRequest{
        Image:        "postgres:15-alpine",
        ExposedPorts: []string{"5432/tcp"},
        Env: map[string]string{
            "POSTGRES_DB":       "testdb",
            "POSTGRES_USER":     "testuser",
            "POSTGRES_PASSWORD": "testpass",
        },
        WaitingFor: wait.ForListeningPort("5432/tcp"),
    }

    container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
        ContainerRequest: req,
        Started:          true,
    })
    assert.NoError(suite.T(), err)

    suite.container = container

    // Get container connection details
    host, err := container.Host(ctx)
    assert.NoError(suite.T(), err)

    port, err := container.MappedPort(ctx, "5432")
    assert.NoError(suite.T(), err)

    // Connect to database
    dsn := fmt.Sprintf("host=%s port=%s user=testuser password=testpass dbname=testdb sslmode=disable", host, port.Port())
    db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
    assert.NoError(suite.T(), err)

    suite.db = db

    // Run migrations
    err = db.AutoMigrate(&model.User{})
    assert.NoError(suite.T(), err)

    // Setup dependencies
    logger := logger.NewNoop()
    repo := repository.NewUserRepository(db)
    svc := service.NewUserService(repo, logger)
    suite.handler = handler.NewUserHandler(svc, logger)

    // Setup router
    gin.SetMode(gin.TestMode)
    suite.router = gin.New()
    v1 := suite.router.Group("/api/v1")
    {
        v1.POST("/users", suite.handler.CreateUser)
        v1.GET("/users/:id", suite.handler.GetUser)
        v1.PUT("/users/:id", suite.handler.UpdateUser)
        v1.DELETE("/users/:id", suite.handler.DeleteUser)
        v1.GET("/users", suite.handler.ListUsers)
    }
}

func (suite *UserIntegrationTestSuite) TearDownSuite() {
    if suite.container != nil {
        suite.container.Terminate(context.Background())
    }
}

func (suite *UserIntegrationTestSuite) SetupTest() {
    // Clean database before each test
    suite.db.Exec("TRUNCATE TABLE users RESTART IDENTITY CASCADE")
}

func (suite *UserIntegrationTestSuite) TestCreateUser_Success() {
    // Arrange
    createReq := model.CreateUserRequest{
        Email:     "<EMAIL>",
        FirstName: "John",
        LastName:  "Doe",
        Password:  "password123",
    }

    body, _ := json.Marshal(createReq)

    // Act
    req, _ := http.NewRequest("POST", "/api/v1/users", bytes.NewBuffer(body))
    req.Header.Set("Content-Type", "application/json")

    w := httptest.NewRecorder()
    suite.router.ServeHTTP(w, req)

    // Assert
    assert.Equal(suite.T(), http.StatusCreated, w.Code)

    var response handler.APIResponse
    err := json.Unmarshal(w.Body.Bytes(), &response)
    assert.NoError(suite.T(), err)
    assert.True(suite.T(), response.Success)

    userData := response.Data.(map[string]interface{})
    assert.Equal(suite.T(), createReq.Email, userData["email"])
    assert.Equal(suite.T(), createReq.FirstName, userData["first_name"])
    assert.Equal(suite.T(), createReq.LastName, userData["last_name"])
}

func TestUserIntegrationTestSuite(t *testing.T) {
    suite.Run(t, new(UserIntegrationTestSuite))
}
```

## 11. API Gateway Implementation

Enterprise microservices need a centralized API Gateway for routing, authentication, and rate limiting:

### API Gateway Service

```go
// cmd/api-gateway/main.go
package main

import (
    "context"
    "log"
    "net/http"
    "net/http/httputil"
    "net/url"
    "strings"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/golang-jwt/jwt/v5"

    "github.com/yourusername/enterprise-microservices/pkg/config"
    "github.com/yourusername/enterprise-microservices/pkg/logger"
    "github.com/yourusername/enterprise-microservices/pkg/middleware"
)

type Gateway struct {
    config   *config.Config
    logger   logger.Logger
    services map[string]*url.URL
}

func NewGateway(cfg *config.Config, logger logger.Logger) *Gateway {
    services := make(map[string]*url.URL)

    // Register microservices
    userServiceURL, _ := url.Parse("http://user-service:8080")
    authServiceURL, _ := url.Parse("http://auth-service:8080")

    services["user"] = userServiceURL
    services["auth"] = authServiceURL

    return &Gateway{
        config:   cfg,
        logger:   logger,
        services: services,
    }
}

func (g *Gateway) setupRoutes() *gin.Engine {
    router := gin.New()

    // Global middleware
    router.Use(gin.Recovery())
    router.Use(middleware.CORS())
    router.Use(middleware.Logger(g.logger))
    router.Use(middleware.RateLimit())

    // Health check
    router.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{"status": "healthy", "service": "api-gateway"})
    })

    // API routes with service routing
    api := router.Group("/api/v1")
    {
        // Authentication routes (public)
        auth := api.Group("/auth")
        auth.Use(g.routeToService("auth"))
        {
            auth.POST("/login", g.proxyHandler)
            auth.POST("/register", g.proxyHandler)
            auth.POST("/refresh", g.proxyHandler)
        }

        // User routes (protected)
        users := api.Group("/users")
        users.Use(g.authMiddleware())
        users.Use(g.routeToService("user"))
        {
            users.GET("", g.proxyHandler)
            users.POST("", g.proxyHandler)
            users.GET("/:id", g.proxyHandler)
            users.PUT("/:id", g.proxyHandler)
            users.DELETE("/:id", g.proxyHandler)
        }
    }

    return router
}

func (g *Gateway) routeToService(serviceName string) gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        serviceURL, exists := g.services[serviceName]
        if !exists {
            c.JSON(http.StatusNotFound, gin.H{"error": "Service not found"})
            c.Abort()
            return
        }

        c.Set("target_service", serviceURL)
        c.Next()
    })
}

func (g *Gateway) authMiddleware() gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
            c.Abort()
            return
        }

        tokenParts := strings.Split(authHeader, " ")
        if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
            c.Abort()
            return
        }

        // Validate JWT token
        token, err := jwt.Parse(tokenParts[1], func(token *jwt.Token) (interface{}, error) {
            return []byte(g.config.JWT.Secret), nil
        })

        if err != nil || !token.Valid {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
            c.Abort()
            return
        }

        c.Next()
    })
}

func (g *Gateway) proxyHandler(c *gin.Context) {
    targetService, exists := c.Get("target_service")
    if !exists {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Target service not set"})
        return
    }

    target := targetService.(*url.URL)
    proxy := httputil.NewSingleHostReverseProxy(target)

    // Modify the request
    proxy.Director = func(req *http.Request) {
        req.URL.Scheme = target.Scheme
        req.URL.Host = target.Host
        req.Host = target.Host

        // Remove gateway-specific headers
        req.Header.Del("X-Gateway-Route")

        // Add tracing headers
        if traceID := c.GetString("trace_id"); traceID != "" {
            req.Header.Set("X-Trace-ID", traceID)
        }
    }

    // Handle errors
    proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
        g.logger.Error("Proxy error", "error", err, "target", target.String())
        w.WriteHeader(http.StatusBadGateway)
        w.Write([]byte(`{"error": "Service unavailable"}`))
    }

    proxy.ServeHTTP(c.Writer, c.Request)
}

func main() {
    cfg, err := config.Load("config.yaml")
    if err != nil {
        log.Fatal("Failed to load config:", err)
    }

    logger := logger.New(cfg.LogLevel)
    gateway := NewGateway(cfg, logger)

    router := gateway.setupRoutes()

    server := &http.Server{
        Addr:         ":8080",
        Handler:      router,
        ReadTimeout:  cfg.Server.ReadTimeout,
        WriteTimeout: cfg.Server.WriteTimeout,
    }

    logger.Info("API Gateway starting on :8080")
    if err := server.ListenAndServe(); err != nil {
        logger.Fatal("Failed to start server", "error", err)
    }
}
```

## 12. Kubernetes Deployment

Enterprise microservices require production-ready deployment configurations:

### Kubernetes Deployment Manifests

```yaml
# deployments/kubernetes/user-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: microservices
  labels:
    app: user-service
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
        version: v1
    spec:
      containers:
        - name: user-service
          image: your-registry/user-service:latest
          ports:
            - containerPort: 8080
              name: http
          env:
            - name: DATABASE_HOST
              value: "postgres-service"
            - name: DATABASE_PORT
              value: "5432"
            - name: DATABASE_USER
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: username
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: password
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: jwt-secret
                  key: secret
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health/live
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health/ready
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            allowPrivilegeEscalation: false
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: microservices
spec:
  selector:
    app: user-service
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      name: http
  type: ClusterIP
```

### Horizontal Pod Autoscaler

```yaml
# deployments/kubernetes/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
  namespace: microservices
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
```

## 13. Monitoring & Observability

Enterprise microservices need comprehensive monitoring and observability:

### Prometheus Metrics

```go
// pkg/metrics/metrics.go
package metrics

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    HttpRequestsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status_code", "service"},
    )

    HttpRequestDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "http_request_duration_seconds",
            Help:    "Duration of HTTP requests",
            Buckets: prometheus.DefBuckets,
        },
        []string{"method", "endpoint", "service"},
    )

    DatabaseConnectionsActive = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "database_connections_active",
            Help: "Number of active database connections",
        },
        []string{"database", "service"},
    )

    BusinessMetricsUsersCreated = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "business_users_created_total",
            Help: "Total number of users created",
        },
        []string{"service"},
    )

    EventsPublished = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "events_published_total",
            Help: "Total number of events published",
        },
        []string{"event_type", "topic", "service"},
    )

    EventsProcessed = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "events_processed_total",
            Help: "Total number of events processed",
        },
        []string{"event_type", "status", "service"},
    )
)

func Init(serviceName string) {
    // Initialize service-specific metrics
    HttpRequestsTotal.WithLabelValues("", "", "", serviceName)
    HttpRequestDuration.WithLabelValues("", "", serviceName)
}
```

### Health Check with Dependencies

```go
// pkg/health/health.go
package health

import (
    "context"
    "encoding/json"
    "net/http"
    "time"

    "github.com/gin-gonic/gin"
)

type HealthChecker struct {
    checks map[string]HealthCheck
}

type HealthCheck interface {
    Check(ctx context.Context) error
    Name() string
}

type HealthStatus struct {
    Status    string            `json:"status"`
    Timestamp time.Time         `json:"timestamp"`
    Service   string            `json:"service"`
    Version   string            `json:"version"`
    Checks    map[string]string `json:"checks"`
    Uptime    string            `json:"uptime"`
}

func NewHealthChecker() *HealthChecker {
    return &HealthChecker{
        checks: make(map[string]HealthCheck),
    }
}

func (h *HealthChecker) AddCheck(check HealthCheck) {
    h.checks[check.Name()] = check
}

func (h *HealthChecker) LivenessHandler(serviceName, version string, startTime time.Time) gin.HandlerFunc {
    return func(c *gin.Context) {
        status := HealthStatus{
            Status:    "alive",
            Timestamp: time.Now().UTC(),
            Service:   serviceName,
            Version:   version,
            Uptime:    time.Since(startTime).String(),
        }

        c.JSON(http.StatusOK, status)
    }
}

func (h *HealthChecker) ReadinessHandler(serviceName, version string, startTime time.Time) gin.HandlerFunc {
    return func(c *gin.Context) {
        ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
        defer cancel()

        checks := make(map[string]string)
        overallStatus := "ready"

        for name, check := range h.checks {
            if err := check.Check(ctx); err != nil {
                checks[name] = "unhealthy: " + err.Error()
                overallStatus = "not ready"
            } else {
                checks[name] = "healthy"
            }
        }

        status := HealthStatus{
            Status:    overallStatus,
            Timestamp: time.Now().UTC(),
            Service:   serviceName,
            Version:   version,
            Checks:    checks,
            Uptime:    time.Since(startTime).String(),
        }

        statusCode := http.StatusOK
        if overallStatus != "ready" {
            statusCode = http.StatusServiceUnavailable
        }

        c.JSON(statusCode, status)
    }
}

// Database health check implementation
type DatabaseHealthCheck struct {
    db interface {
        Ping() error
    }
}

func (d *DatabaseHealthCheck) Check(ctx context.Context) error {
    return d.db.Ping()
}

func (d *DatabaseHealthCheck) Name() string {
    return "database"
}
```

## 14. Security Best Practices

Enterprise microservices must implement robust security measures:

### Security Middleware

```go
// pkg/middleware/security.go
package middleware

import (
    "crypto/subtle"
    "net/http"
    "strings"

    "github.com/gin-gonic/gin"
)

// Security headers middleware
func SecurityHeaders() gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        // Prevent clickjacking
        c.Header("X-Frame-Options", "DENY")

        // Prevent MIME type sniffing
        c.Header("X-Content-Type-Options", "nosniff")

        // XSS protection
        c.Header("X-XSS-Protection", "1; mode=block")

        // Strict transport security (HTTPS only)
        c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")

        // Content security policy
        c.Header("Content-Security-Policy", "default-src 'self'")

        // Referrer policy
        c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

        c.Next()
    })
}

// API key authentication middleware
func APIKeyAuth(validAPIKeys map[string]string) gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        apiKey := c.GetHeader("X-API-Key")
        if apiKey == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "API key required"})
            c.Abort()
            return
        }

        // Constant time comparison to prevent timing attacks
        var found bool
        for key, service := range validAPIKeys {
            if subtle.ConstantTimeCompare([]byte(apiKey), []byte(key)) == 1 {
                found = true
                c.Set("api_service", service)
                break
            }
        }

        if !found {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid API key"})
            c.Abort()
            return
        }

        c.Next()
    })
}

// Request size limiting middleware
func RequestSizeLimit(maxSize int64) gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        if c.Request.ContentLength > maxSize {
            c.JSON(http.StatusRequestEntityTooLarge, gin.H{
                "error": "Request body too large",
                "max_size": maxSize,
            })
            c.Abort()
            return
        }

        c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, maxSize)
        c.Next()
    })
}
```

### Input Validation & Sanitization

```go
// pkg/validation/validator.go
package validation

import (
    "regexp"
    "strings"
    "unicode"

    "github.com/go-playground/validator/v10"
)

func NewValidator() *validator.Validate {
    validate := validator.New()

    // Custom validation rules
    validate.RegisterValidation("strong_password", validateStrongPassword)
    validate.RegisterValidation("no_sql_injection", validateNoSQLInjection)
    validate.RegisterValidation("safe_string", validateSafeString)

    return validate
}

func validateStrongPassword(fl validator.FieldLevel) bool {
    password := fl.Field().String()

    if len(password) < 8 {
        return false
    }

    var hasUpper, hasLower, hasNumber, hasSpecial bool

    for _, char := range password {
        switch {
        case unicode.IsUpper(char):
            hasUpper = true
        case unicode.IsLower(char):
            hasLower = true
        case unicode.IsNumber(char):
            hasNumber = true
        case unicode.IsPunct(char) || unicode.IsSymbol(char):
            hasSpecial = true
        }
    }

    return hasUpper && hasLower && hasNumber && hasSpecial
}

func validateNoSQLInjection(fl validator.FieldLevel) bool {
    value := strings.ToLower(fl.Field().String())

    // Basic SQL injection patterns
    patterns := []string{
        "select", "insert", "update", "delete", "drop", "create",
        "alter", "exec", "execute", "union", "script", "javascript",
        "'", "\"", ";", "--", "/*", "*/", "xp_", "sp_",
    }

    for _, pattern := range patterns {
        if strings.Contains(value, pattern) {
            return false
        }
    }

    return true
}

func validateSafeString(fl validator.FieldLevel) bool {
    value := fl.Field().String()

    // Allow only alphanumeric, spaces, and safe punctuation
    safePattern := regexp.MustCompile(`^[a-zA-Z0-9\s\-_.,!?@]+$`)
    return safePattern.MatchString(value)
}

func SanitizeInput(input string) string {
    // Remove potentially dangerous characters
    input = strings.ReplaceAll(input, "<", "&lt;")
    input = strings.ReplaceAll(input, ">", "&gt;")
    input = strings.ReplaceAll(input, "\"", "&quot;")
    input = strings.ReplaceAll(input, "'", "&#x27;")
    input = strings.ReplaceAll(input, "&", "&amp;")

    return strings.TrimSpace(input)
}
```

## 15. Complete Application Assembly

Let's put everything together in a complete enterprise microservice:

### Main Application

```go
// cmd/user-service/main.go
package main

import (
    "context"
    "log"
    "os"
    "os/signal"
    "syscall"
    "time"

    "github.com/yourusername/enterprise-microservices/internal/auth/service"
    "github.com/yourusername/enterprise-microservices/internal/user/handler"
    "github.com/yourusername/enterprise-microservices/internal/user/repository"
    userService "github.com/yourusername/enterprise-microservices/internal/user/service"
    "github.com/yourusername/enterprise-microservices/pkg/config"
    "github.com/yourusername/enterprise-microservices/pkg/database"
    "github.com/yourusername/enterprise-microservices/pkg/events"
    "github.com/yourusername/enterprise-microservices/pkg/health"
    "github.com/yourusername/enterprise-microservices/pkg/logger"
    "github.com/yourusername/enterprise-microservices/pkg/server"
    "github.com/yourusername/enterprise-microservices/pkg/tracing"
)

func main() {
    // Load configuration
    cfg, err := config.Load("config.yaml")
    if err != nil {
        log.Fatal("Failed to load config:", err)
    }

    // Initialize logger
    logger := logger.New(cfg.LogLevel)
    logger.Info("Starting user service", "version", "1.0.0")

    // Initialize tracing
    tracerProvider, err := tracing.NewTracerProvider(&cfg.Tracing, logger)
    if err != nil {
        logger.Fatal("Failed to initialize tracing", "error", err)
    }
    defer tracerProvider.Shutdown(context.Background())

    // Initialize database
    db, err := database.NewPostgresConnection(&cfg.Database, logger)
    if err != nil {
        logger.Fatal("Failed to connect to database", "error", err)
    }
    defer db.Close()

    // Run migrations
    if err := db.RunMigrations(); err != nil {
        logger.Fatal("Failed to run migrations", "error", err)
    }

    // Initialize Redis for events
    redisClient := redis.NewClient(&redis.Options{
        Addr:     fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
        Password: cfg.Redis.Password,
        DB:       cfg.Redis.DB,
    })
    defer redisClient.Close()

    // Initialize event bus
    eventBus := events.NewRedisEventBus(redisClient, logger)

    // Initialize services
    userRepo := repository.NewUserRepository(db.DB)
    userSvc := userService.NewUserService(userRepo, logger)
    jwtSvc := service.NewJWTService(&cfg.JWT)

    // Initialize handlers
    userHandler := handler.NewUserHandler(userSvc, logger)

    // Initialize health checker
    healthChecker := health.NewHealthChecker()
    healthChecker.AddCheck(&health.DatabaseHealthCheck{DB: db.DB})

    // Initialize server
    srv := server.New(cfg, logger)
    srv.SetupMiddleware()
    srv.SetupRoutes(userHandler, jwtSvc, healthChecker)

    // Start server
    startTime := time.Now()
    logger.Info("User service started successfully", "startup_time", time.Since(startTime))

    if err := srv.Start(); err != nil {
        logger.Fatal("Failed to start server", "error", err)
    }
}
```

## 16. Production Deployment Checklist

Before deploying to production, ensure you have:

### Security Checklist

- ✅ **HTTPS/TLS** enabled for all external communication
- ✅ **JWT tokens** with proper expiration and rotation
- ✅ **Input validation** and sanitization implemented
- ✅ **Rate limiting** configured appropriately
- ✅ **Security headers** added to all responses
- ✅ **Secrets management** using Kubernetes secrets or external vaults
- ✅ **Network policies** restricting inter-service communication
- ✅ **Container security** with non-root users and read-only filesystems

### Observability Checklist

- ✅ **Structured logging** with correlation IDs
- ✅ **Metrics collection** with Prometheus
- ✅ **Distributed tracing** with Jaeger/Zipkin
- ✅ **Health checks** for liveness and readiness
- ✅ **Alerting rules** for critical metrics
- ✅ **Dashboard** setup for monitoring
- ✅ **Log aggregation** with ELK stack or similar

### Reliability Checklist

- ✅ **Circuit breakers** for external dependencies
- ✅ **Retry mechanisms** with exponential backoff
- ✅ **Graceful shutdown** handling
- ✅ **Database connection pooling** configured
- ✅ **Resource limits** set in Kubernetes
- ✅ **Horizontal Pod Autoscaler** configured
- ✅ **Backup and recovery** procedures tested

<Callout type="warning">
  Never deploy to production without proper testing, monitoring, and rollback
  procedures in place.
</Callout>

## Conclusion

Congratulations! You've built a complete enterprise-level microservices system with Go. This tutorial covered everything needed for production-ready microservices:

### 🏗️ **Architecture & Design**

- **Enterprise Project Structure** with proper DDD organization
- **Domain-Driven Design** with clear separation of concerns
- **Repository Pattern** for data access abstraction
- **Service Layer** with comprehensive business logic
- **API Gateway** for centralized routing and security

### 🔐 **Security & Authentication**

- **JWT-based Authentication** with access/refresh tokens
- **Role-based Authorization** with middleware
- **Input Validation** and sanitization
- **Security Headers** and protection mechanisms
- **API Key Authentication** for service-to-service communication

### 🚀 **Scalability & Performance**

- **Circuit Breaker Pattern** for fault tolerance
- **Retry Mechanisms** with exponential backoff
- **Connection Pooling** for database optimization
- **Rate Limiting** for API protection
- **Horizontal Pod Autoscaling** for dynamic scaling

### 📊 **Observability & Monitoring**

- **Distributed Tracing** with OpenTelemetry
- **Prometheus Metrics** collection
- **Structured Logging** with correlation IDs
- **Health Checks** for liveness and readiness
- **Comprehensive Testing** strategies

### 🐳 **Deployment & Operations**

- **Kubernetes Deployment** configurations
- **Docker Containerization** with multi-stage builds
- **Event-Driven Architecture** with Redis Streams
- **Database Migrations** and connection management
- **Graceful Shutdown** handling

<Callout type="success">
  You now have the knowledge to build enterprise-grade microservices that can
  handle millions of requests, scale automatically, and maintain high
  availability in production environments.
</Callout>

## Enterprise Best Practices Implemented

✅ **Microservices Architecture** - Complete service separation and independence
✅ **Domain-Driven Design** - Proper domain modeling and bounded contexts
✅ **SOLID Principles** - Interface-based design and dependency injection
✅ **12-Factor App** - Configuration, logging, and stateless design
✅ **Circuit Breaker Pattern** - Resilience to cascading failures
✅ **Event Sourcing** - Asynchronous communication and audit trails
✅ **CQRS Pattern** - Command and query responsibility segregation
✅ **Observability** - Comprehensive monitoring and tracing
✅ **Security by Design** - Authentication, authorization, and input validation
✅ **Infrastructure as Code** - Kubernetes manifests and deployment automation

## Production-Ready Features

🔒 **Security**: JWT authentication, RBAC, input validation, security headers
📈 **Scalability**: Auto-scaling, load balancing, connection pooling
🔍 **Observability**: Distributed tracing, metrics, structured logging
🛡️ **Reliability**: Circuit breakers, retries, health checks, graceful shutdown
🚀 **Performance**: Optimized database queries, caching, rate limiting
🔧 **Maintainability**: Clean architecture, comprehensive testing, documentation

## Next Steps for Enterprise Development

1. **Service Mesh**: Implement Istio for advanced traffic management
2. **API Versioning**: Add comprehensive API versioning strategies
3. **Data Consistency**: Implement Saga pattern for distributed transactions
4. **Caching**: Add Redis caching layers for improved performance
5. **Message Queues**: Integrate Apache Kafka for high-throughput events
6. **Service Discovery**: Implement Consul or etcd for dynamic service discovery
7. **CI/CD Pipeline**: Set up GitOps with ArgoCD for automated deployments
8. **Chaos Engineering**: Implement chaos testing with tools like Chaos Monkey

## Enterprise Libraries & Tools

- **Web Framework**: Gin (production-ready with excellent performance)
- **Database**: GORM + PostgreSQL (enterprise-grade persistence)
- **Configuration**: Viper (flexible configuration management)
- **Logging**: Logrus/Zap (structured logging with performance)
- **Testing**: Testify + Testcontainers (comprehensive testing)
- **Validation**: go-playground/validator (robust input validation)
- **Tracing**: OpenTelemetry (industry-standard observability)
- **Metrics**: Prometheus (de facto standard for metrics)

You're now equipped to build and deploy enterprise-level microservices that can compete with systems at companies like Google, Netflix, and Uber. The patterns and practices you've learned are battle-tested in production environments serving millions of users.

**Happy building enterprise microservices with Go!** 🚀⚡
