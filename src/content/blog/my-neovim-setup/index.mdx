---
title: "My Neovim Setup: A Modern Development Environment"
description: "Deep dive into my modular, minimalist Neovim configuration with LSP, DAP, Treesitter, and extensive language support"
date: "2024-07-02"
lastUpdateDate: "2024-07-02"
ogImage: "/blog/my-neovim-setup/neovim-setup.png"
tags:
  - neovim
  - lua
  - editor
  - development
  - productivity
  - tools
---

import Callout from "@components/Callout.astro";
import MdxPublicImage from "@components/MdxPublicImage.astro";
import ProjectBadges from "@components/ProjectBadges.astro";
import TechStack from "@components/TechStack.astro";

After years of using various editors and IDEs, I've settled on Neovim as my primary development environment. What started as curiosity about modal editing has evolved into a highly customized, efficient setup that I can't imagine working without.

<ProjectBadges
  project="custom"
  technologies={["neovim", "lua", "git", "github"]}
  variant="flat"
/>

🔗 **[View Configuration on GitHub](https://github.com/valonmulolli/nvim)**

## Why Neovim?

<MdxPublicImage
  src="/blog/my-neovim-setup/neovim.jpg"
  alt="My Neovim setup in action"
/>

Coming from VS Code and JetBrains IDEs, the switch to Neovim wasn't immediate. But once I experienced the speed, customizability, and modal editing paradigm, there was no going back. Here's what convinced me:

### **Performance & Speed**

- **Instant startup** - No waiting for heavy IDEs to load
- **Minimal resource usage** - Runs smoothly even on older hardware
- **Fast file navigation** - Telescope and fuzzy finding make large codebases manageable
- **Responsive editing** - No lag, even with large files

### **Customizability**

- **Everything is configurable** - From keybindings to UI elements
- **Lua configuration** - Modern, fast scripting language
- **Plugin ecosystem** - Thousands of plugins for any workflow
- **Personal workflow** - Tailored exactly to how I work

### **Modal Editing**

Once you learn Vim motions, you realize how inefficient mouse-driven editing is. The ability to navigate and edit text without leaving the home row is transformative.

## Configuration Philosophy

My Neovim setup follows these principles:

### **Modular Architecture**

```
~/.config/nvim/
├── after/                 # Filetype-specific overrides
├── lua/
│   ├── config/           # Core configuration
│   ├── plugins/          # Plugin configurations
│   ├── lualine/          # Custom statusline themes
│   └── telescope/        # Search extensions
├── init.lua              # Entry point
└── lazy-lock.json        # Plugin version lock
```

### **Lazy Loading**

Using [lazy.nvim](https://github.com/folke/lazy.nvim) for plugin management ensures fast startup times by loading plugins only when needed.

### **Language-First Approach**

Every language I work with gets first-class support through LSP, formatters, linters, and debuggers.

## Core Features

<TechStack
  technologies={["neovim", "lua", "go", "rust", "python", "typescript"]}
  title="Supported Languages & Technologies"
  variant="flat"
/>

### **Language Server Protocol (LSP)**

- **Mason.nvim** for automatic LSP server management
- **Custom configurations** for Go, Rust, Python, TypeScript, and more
- **Intelligent completion** with nvim-cmp
- **Real-time diagnostics** and error highlighting
- **Code actions** and refactoring support

### **Debug Adapter Protocol (DAP)**

- **nvim-dap** for debugging support
- **Language-specific adapters** for Go, Python, Node.js
- **Visual debugging** with breakpoints and variable inspection
- **Integrated terminal** for debug console

### **Advanced Text Editing**

- **Treesitter** for syntax highlighting and code understanding
- **Surround.nvim** for manipulating brackets, quotes, and tags
- **Autopairs** for automatic bracket completion
- **Multiple cursors** and visual block editing
- **Smart indentation** and formatting

### **Git Integration**

- **Gitsigns** for inline git blame and diff markers
- **Neogit** for comprehensive git operations
- **Diffview** for reviewing changes and conflicts
- **Fugitive** for advanced git workflows

## Key Plugins & Configurations

### **Plugin Manager: Lazy.nvim**

```lua
-- Automatic plugin installation and lazy loading
require("lazy").setup({
  spec = {
    { import = "plugins" },
  },
  performance = {
    rtp = {
      disabled_plugins = {
        "gzip", "matchit", "matchparen",
        "netrwPlugin", "tarPlugin", "tohtml",
        "tutor", "zipPlugin",
      },
    },
  },
})
```

### **LSP Configuration**

```lua
-- Mason for LSP server management
require("mason").setup()
require("mason-lspconfig").setup({
  ensure_installed = {
    "gopls", "rust_analyzer", "pyright",
    "tsserver", "lua_ls", "bashls"
  }
})

-- Custom LSP handlers
local on_attach = function(client, bufnr)
  -- Custom keybindings and capabilities
end
```

### **Completion Setup**

```lua
-- nvim-cmp for intelligent completion
local cmp = require("cmp")
cmp.setup({
  sources = {
    { name = "nvim_lsp" },
    { name = "luasnip" },
    { name = "copilot" },
    { name = "buffer" },
    { name = "path" },
  },
})
```

## Custom Keybindings

My keybinding philosophy centers around the space key as the leader, with logical groupings:

### **Navigation & Files**

- `<Space>ff` - Find files with Telescope
- `<Space>fg` - Live grep search
- `<Space>fb` - Browse buffers
- `<Space>fr` - Recent files
- `<Tab>` / `<S-Tab>` - Next/previous buffer

### **LSP Operations**

- `gd` - Go to definition
- `gr` - Find references
- `K` - Show hover documentation
- `<Space>ca` - Code actions
- `<Space>cr` - Rename symbol

### **Git Operations**

- `<Space>gs` - Git status (Neogit)
- `<Space>gb` - Git blame
- `<Space>gd` - Git diff
- `]c` / `[c` - Next/previous git hunk

### **Code Execution**

- `<Space>r` - Run current file
- `<Space>rp` - Run project
- `<Space>rt` - Run in new tab

## Language-Specific Features

### **Go Development**

- **gopls** LSP with custom configuration
- **go.nvim** for enhanced Go support
- **Debugging** with Delve integration
- **Test running** and coverage
- **Struct tags** and interface implementation

### **Rust Development**

- **rust-analyzer** with full feature set
- **Cargo integration** for building and testing
- **Clippy** linting and suggestions
- **Debugging** with CodeLLDB
- **Crate management** and documentation

### **Python Development**

- **Pyright** for type checking
- **Black** formatting
- **isort** import sorting
- **pytest** integration
- **Virtual environment** detection

### **Web Development**

- **TypeScript** support with tsserver
- **ESLint** and Prettier integration
- **Tailwind CSS** IntelliSense
- **React/Vue** component support
- **Emmet** for HTML/CSS

## Themes & Aesthetics

I use multiple colorschemes depending on the context:

### **Lackluster (Default)**

A minimal, low-contrast theme that's easy on the eyes during long coding sessions.

### **Gruvbox**

Classic retro theme with warm colors, perfect for terminal work.

### **Tokyo Night**

Modern dark theme with vibrant colors for web development.

### **Custom Lualine**

My statusline shows:

- Current mode with color coding
- Git branch and changes
- LSP status and diagnostics
- File encoding and type
- Cursor position

## Productivity Features

### **Session Management**

Automatic session saving and restoration means I can pick up exactly where I left off on any project.

### **Project Navigation**

- **Telescope** for fuzzy finding everything
- **Hop.nvim** for quick cursor movement
- **Project.nvim** for project switching
- **Oil.nvim** for file management

### **Terminal Integration**

- **ToggleTerm** for integrated terminals
- **Smart window management** for seamless workflow
- **REPL integration** for interactive development

### **Code Runner**

Custom code runner that understands different project types:

- **Go**: `go run` for single files, `go build` for projects
- **Rust**: `cargo run` with automatic binary detection
- **Python**: Virtual environment aware execution
- **Node.js**: Package.json script detection

## Performance Optimizations

### **Startup Time**

My configuration starts in under 50ms thanks to:

- **Lazy loading** of all plugins
- **Disabled unused** built-in plugins
- **Optimized** plugin configurations
- **Minimal** initial setup

### **Memory Usage**

- **Treesitter** parsers loaded on demand
- **LSP servers** started per project
- **Buffers** automatically cleaned up
- **Plugin** memory management

## Installation & Setup

<Callout type="info">
  My configuration is designed to work out of the box with minimal setup
  required.
</Callout>

### **Prerequisites**

```bash
# Required
brew install neovim git ripgrep fd
# Install a Nerd Font for icons

# Language servers (installed automatically via Mason)
# But you'll need the languages themselves:
brew install go rust python node
```

### **Installation**

```bash
# Backup existing config
mv ~/.config/nvim ~/.config/nvim.backup

# Clone configuration
git clone https://github.com/valonmulolli/nvim ~/.config/nvim

# Launch Neovim (plugins install automatically)
nvim
```

### **First Launch**

On first launch, Lazy.nvim will automatically:

1. Install all plugins
2. Set up LSP servers via Mason
3. Configure Treesitter parsers
4. Initialize all configurations

## Tips for Neovim Newcomers

### **Learning Curve**

- **Start with basic Vim motions** - `hjkl`, `w`, `b`, `e`
- **Learn one new motion per day** - Don't overwhelm yourself
- **Use Vimtutor** - Built-in tutorial is excellent
- **Practice consistently** - Muscle memory takes time

### **Configuration Approach**

- **Start minimal** - Don't copy entire configs
- **Understand each plugin** - Know what everything does
- **Customize gradually** - Add features as you need them
- **Read documentation** - `:help` is your friend

### **Common Mistakes**

- **Too many plugins** - Start with essentials
- **Complex keybindings** - Keep them logical and memorable
- **Ignoring defaults** - Vim's defaults are often good
- **Not learning motions** - The real power is in modal editing

## Future Improvements

I'm constantly evolving my setup. Current areas of focus:

### **AI Integration**

- **GitHub Copilot** for code completion
- **ChatGPT integration** for code explanation
- **AI-powered** refactoring suggestions

### **Better Language Support**

- **Zig** and **Odin** language servers
- **WebAssembly** development tools
- **Database** query and schema tools

### **Workflow Enhancements**

- **Note-taking** integration with Obsidian
- **Task management** within Neovim
- **Time tracking** for productivity metrics

## Conclusion

My Neovim setup represents years of iteration and refinement. It's not just an editor—it's a complete development environment tailored to my workflow. The investment in learning Vim motions and Lua configuration has paid dividends in productivity and coding enjoyment.

<Callout type="success">
  The beauty of Neovim is that it grows with you. Start simple, add complexity
  as needed, and always prioritize understanding over copying.
</Callout>

Whether you're a seasoned developer looking to optimize your workflow or a newcomer curious about modal editing, I encourage you to give Neovim a try. The learning curve is steep, but the rewards are substantial.

**What's your development environment of choice? Have you tried Neovim, or are you considering making the switch?**

---

_Want to see more content like this? Follow me for more deep dives into development tools, programming languages, and productivity tips._
