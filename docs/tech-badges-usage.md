# Tech Badges Usage Guide

This guide shows how to use the custom tech badge components in your blog and tutorials.

## Components

### 1. TechBadge - Single Technology Badge

Display a single technology badge:

```astro
---
import TechBadge from "@components/TechBadge.astro";
---

<TechBadge tech="go" variant="flat" link={true} />
<TechBadge tech="rust" variant="for-the-badge" />
<TechBadge tech="typescript" color="blue" />
```

**Props:**

- `tech` (required): Technology name (see supported list below)
- `variant`: 'default' | 'flat' | 'for-the-badge' (default: 'default')
- `color`: Custom color override
- `link`: Whether to make badge clickable (default: false)

### 2. TechStack - Multiple Technology Badges

Display multiple badges with a title:

```astro
---
import TechStack from "@components/TechStack.astro";
---

<!-- Custom array of technologies -->
<TechStack
  technologies={["go", "docker", "kubernetes"]}
  title="Backend Stack"
  variant="flat"
  link={true}
/>

<!-- Predefined stacks -->
<TechStack technologies="microservices" title="Microservices Stack" />
<TechStack technologies="frontend" title="Frontend Technologies" />
```

**Predefined Stacks:**

- `frontend`: astro, typescript, tailwind, javascript
- `backend`: go, rust, python, flask
- `database`: postgresql, redis
- `devops`: docker, kubernetes, linux, arch
- `tools`: git, github, vscode
- `dataengineering`: python, apache-kafka, postgresql, prometheus, grafana
- `cloud`: aws, gcp, docker, kubernetes
- `microservices`: go, docker, kubernetes, postgresql, redis, prometheus
- `webdev`: astro, typescript, tailwind, javascript, git, github
- editor: neovim, lua, git, github

### 3. ProjectBadges - Project-Specific Badges

Display badges for specific projects:

```astro
---
import ProjectBadges from "@components/ProjectBadges.astro";
---

<ProjectBadges project="ciphervault" variant="flat" />
<ProjectBadges project="microservices" />
<ProjectBadges
  project="custom"
  technologies={["python", "flask", "postgresql"]}
/>
```

**Predefined Projects:**

- `ciphervault`: rust, linux, git, github
- `microservices`: go, docker, kubernetes, postgresql, redis, prometheus, grafana
- `blog`: astro, typescript, tailwind, github
- `dataengineering`: python, postgresql, apache-kafka, docker, kubernetes, prometheus

## Supported Technologies

### Frontend/Web

- astro, typescript, tailwind, javascript

### Backend/Languages

- go, rust, python, flask

### Databases

- postgresql, redis

### DevOps/Infrastructure

- docker, kubernetes, linux, arch

### Tools

- git, github, vscode, neovim, lua

### Data Engineering

- apache-kafka, prometheus, grafana

### Cloud Platforms

- aws, gcp

## Usage Examples

### In Blog Posts (MDX)

```mdx
---
title: "My Project"
---

import ProjectBadges from "@components/ProjectBadges.astro";
import TechStack from "@components/TechStack.astro";

## Project Overview

<ProjectBadges project="microservices" />

This project uses the following technologies:

<TechStack
  technologies={["go", "docker", "postgresql"]}
  variant="flat"
  link={true}
/>
```

### In Tutorial Pages

```mdx
---
title: "Go Tutorial"
---

import TechStack from "@components/TechStack.astro";

<TechStack
  technologies="microservices"
  title="What You'll Learn"
  variant="for-the-badge"
/>
```

### Custom Styling

The badges automatically adapt to your site's theme and include hover effects. You can customize the appearance by:

1. Modifying the CSS in the component files
2. Using the `color` prop for custom colors
3. Choosing different variants for different contexts

## Adding New Technologies

To add a new technology badge:

1. Open `src/components/TechBadge.astro`
2. Add the new entry to the `badges` object:

```javascript
'newtechnology': {
  url: 'https://img.shields.io/badge/NewTech-COLOR?style={style}&logo=logoname&logoColor=white',
  link: 'https://newtechnology.com/'
}
```

3. Use the shield URL format from [md-badges](https://github.com/inttter/md-badges) or [shields.io](https://shields.io/)
