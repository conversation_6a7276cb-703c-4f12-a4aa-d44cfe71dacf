# <PERSON>on <PERSON>lli - Personal Portfolio

[![Portfolio Preview](./public/projects/valon/valon-banner.png)](https://valonmulolli.vercel.app)

<div align="center">

[Live Website](https://valonmulolli.vercel.app) | [Source Code](https://github.com/valonmulolli/valon)

</div>

## About Me

Hello! I'm <PERSON><PERSON> (v470n), a passionate software engineer specializing in enterprise-level systems and microservices architecture. This is my personal portfolio and blog where I share in-depth technical tutorials, projects, and insights on modern software development.

## 📚 Featured Content

### Enterprise Tutorials

- **Building Enterprise-Level Microservices with Go** - Complete 90-minute guide covering authentication, database management, circuit breakers, event-driven architecture, and Kubernetes deployment
- **Rust Language Features** - Comprehensive guide to Rust's unique features including ownership, borrowing, pattern matching, and concurrency

### Technical Focus Areas

- Microservices Architecture & Design Patterns
- Go & Rust Programming Languages
- Data Engineering & Pipeline Development
- Cloud-Native Development & Kubernetes
- System Design & Scalability

## Features

- **Enterprise Tutorials** - In-depth, production-ready guides
- **Portfolio** - Showcase of my projects and work
- **Technical Blog** - Advanced software engineering content
- **Responsive Design** - Works on all devices
- **Dark/Light Mode** - Easy on the eyes, day or night
- **Fast & SEO Optimized** - Built with Astro for optimal performance
- **Full-Text Search** - Powered by Pagefind

## Tech Stack

- [Astro](https://astro.build) - The web framework for content-driven websites
- [Tailwind CSS](https://tailwindcss.com) - For styling
- [MDX](https://mdxjs.com) - For rich content
- Dark/Light mode
- Built-in search functionality

## Getting Started

1. Clone the repository

   ```bash
   git clone https://github.com/valonmulolli/valon.git
   cd valon
   ```

2. Install dependencies

   ```bash
   npm install
   ```

3. Start the development server

   ```bash
   npm run dev
   ```

4. Open [http://localhost:4321](http://localhost:4321) in your browser

## License

This project is open source and available under the [MIT License](LICENSE).

## Connect with Me

- [GitHub](https://github.com/valonmulolli)
- [Twitter](https://twitter.com/valonmulolli)
- [LinkedIn](https://linkedin.com/in/valonmulolli)
- [Email](mailto:<EMAIL>)
