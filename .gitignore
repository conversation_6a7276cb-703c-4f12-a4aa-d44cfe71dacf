# build output
dist/
# generated types
.astro/

# dependencies
node_modules/

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
*.log

# environment variables
.env
.env.production
.env.local
.env.development

# macOS-specific files
.DS_Store

# Windows-specific files
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux-specific files
*~

# jetbrains setting folder
.idea/

# VSCode settings
.vscode/
*.code-workspace

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Editor files
*.xcf
*.psd

# OS generated files
.DS_Store?
._*
.Spotlight-V100
.Trashes
Icon?

# Development scripts
dev.sh
*.dev.sh

# TODO and notes
todo.md
TODO.md
notes.md
NOTES.md
